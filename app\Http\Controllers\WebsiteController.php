<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\UsersMoves;
use App\Models\Webslider;
use App\Models\About;
use App\Models\AcccountingManual;
use App\Models\SocialMedia;
use App\Models\ContactUS;
use App\Models\MsgRqst;
use App\Models\Articles;
use App\Models\Polices;
use App\Models\Products;
use App\Models\Terms;
use App\Models\Virables;
use App\Models\CouponCode;
use App\Models\Governrate;
use App\Models\Addressses;
use App\Models\Customers;
use App\Models\StoresDefaultData;
use App\Models\ProductsQty;
use App\Models\Coins;
use App\Models\ProductsVira;
use App\Models\CompanyData;
use App\Models\CatPro;
use App\Models\User;
use App\Models\ItemsGroups;
use App\Models\Brands;
use App\Models\SubImages;
use App\Models\SubVirables;
use App\Models\ProductUnits;
use App\Models\CostCenter;
use App\Models\Stores;
use App\Models\SafesBanks;

use App\Models\Employess;
use App\Models\Sales;
use App\Models\FAQ;
use App\Models\Countris;
use App\Models\SalesDefaultData;
use App\Models\ProDetailsImg;
use App\Models\BefroeFooter;
use App\Models\ProductSalesOrder;
use App\Models\Rate;
use App\Models\Comments;
use App\Models\SalesOrder;
use App\Models\ProductsPurchases;
use App\Models\Services;
use App\Models\Blog;
use App\Models\Team;
use App\Models\Testimonials;
use App\Models\WebsiteFeatures;
use App\Models\HowWeWork;
use App\Models\Gallery;
use App\Models\Categories;
use App\Models\Testiminoals;
use App\Models\HowWorkIcons;
use App\Models\Privacy;
use App\Models\Footer;
use App\Models\Features;
use App\Models\Groups;
use App\Models\ProductsStoresTransfers;
use App\Models\ProductsStartPeriods;
use App\Models\OutcomManufacturingModel;
use App\Models\ProductMoves;
use App\Models\ChecksTypes;
use App\Models\Journalizing;
use App\Models\JournalizingDetails;
use App\Models\GeneralDaily;
use App\Models\MainEComDesign;
use App\Models\HomeEComDesign;
use App\Models\HomeProductEComDesign;
use App\Models\SupPagesEComDesign;
use App\Models\SupPagesPartTwoEComDesign;
use App\Models\SupPagesWishCompEComDesign;
use App\Models\ProductDetailsEComDesign;
use App\Models\Notifications;
use App\Models\FifoQty;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;
use DB;
use Str;
use Mail;
use Cart;
use Hash;
use SpamProtector;
use App\Mail\UserResetPassword;
class WebsiteController extends Controller
{
    function __construct(){
$this->middleware('permission:سلايدر الموقع', ['only' => ['WebSliderPage']]);
$this->middleware('permission:من نحن', ['only' => ['AboutPage']]);
$this->middleware('permission:التواصل الاجتماعي', ['only' => ['SocialMediaPage']]);
$this->middleware('permission:رسايل التواصل', ['only' => ['MsgRqstPage']]);
$this->middleware('permission:التواصل معنا', ['only' => ['ContactUSPage']]);
$this->middleware('permission:المقالات', ['only' => ['ArticlesPage']]);
$this->middleware('permission:السياسات', ['only' => ['PolicesPage']]);
$this->middleware('permission:الشروط و الاحكام', ['only' => ['TermsPage']]);
$this->middleware('permission:كوبون كود', ['only' => ['CouponCodePage']]);
$this->middleware('permission:طلبات المتجر', ['only' => ['ShopOrders']]);
$this->middleware('permission:الاسئله الشائعه', ['only' => ['FAQPage']]);
$this->middleware('permission:معرض الصور', ['only' => ['GalleryPage']]);
$this->middleware('permission:الخدمات', ['only' => ['ServicesPage']]);
$this->middleware('permission:المدونة', ['only' => ['BlogPage']]);
$this->middleware('permission:فريق العمل', ['only' => ['TeamPage']]);
$this->middleware('permission:آراء العملاء', ['only' => ['TestimonialsPage']]);
$this->middleware('permission:المميزات الإضافية', ['only' => ['WebsiteFeaturesPage']]);
$this->middleware('permission:الدول', ['only' => ['CountrisPage']]);
$this->middleware('permission:اعلانات تفاصيل االمنتج', ['only' => ['ProDetailsImg']]);
$this->middleware('permission:صور سياسه المتجر', ['only' => ['BefroeFooter']]);
$this->middleware('permission:طلبات المتجر', ['only' => ['ShopOrders']]);

}




        //Mtwst Sa3r Sharaa
       private function AverageCost($TotaNewCost,$newQty,$product,$code,$store,$date,$price){


                             $def=StoresDefaultData::orderBy('id','desc')->first();


           if($def->Cost_Price == 1){


           $lastOperation=ProductMoves::orderBy('id','desc')->where('P_Code',$code)->where('Product',$product)->where('Store',$store)->first();


           if(!empty($lastOperation)){
           $AVERAGE = ($lastOperation->CostCurrent + $TotaNewCost) /  ($lastOperation->Current + $newQty) ;
           }else{
           $AVERAGE = ($TotaNewCost) / $newQty ;
           }


   }elseif($def->Cost_Price == 0){


              $PROO=ProductsPurchases::orderBy('id','desc')->where('Product_Code',$code)->where('Product',$product)->where('Store',$store)->first();
               $PROOStart=ProductsStartPeriods::orderBy('id','desc')->where('P_Code',$code)->where('Product',$product)->where('Store',$store)->first();
                    $rr=ProductUnits::where('Product',$product)->where('Def',1)->first();

                   if(!empty($PROO)){
                         $AVERAGE=$PROO->Price;
                   }else{


                       if(!empty($PROOStart)){

                             $AVERAGE = $PROOStart->Price ;
                       }else{
                          $AVERAGE = $rr->Price ;
                       }


                   }




           }elseif($def->Cost_Price == 2){


                     $fifo =FifoQty::orderBy('id','asc')
                 ->where('Store',$store)
              ->where('Product',$product)
    ->where('P_Code',$code)
                ->where('Qty','!=',0)
                ->first();



                  if(empty($fifo)){

  $fifo =FifoQty::orderBy('id','asc')
                  ->where('Store',$store)
                ->where('Product',$product)
                ->where('PP_Code',$code)
                   ->where('Qty','!=',0)
                ->first();

if(empty($fifo)){

  $fifo =FifoQty::orderBy('id','asc')
             ->where('Store',$store)
                ->where('Product',$product)
                ->where('PPP_Code',$code)
                  ->where('Qty','!=',0)
                ->first();


if(empty($fifo)){

  $fifo =FifoQty::orderBy('id','asc')
           ->where('Store',$store)
                ->where('Product',$product)
                ->where('PPPP_Code',$code)
      ->where('Qty','!=',0)
                ->first();

}

}

}



               if(!empty($fifo)){

          $AVERAGE = $fifo->Cost_price ;
               }else{

                 $AVERAGE = $price ;

               }




           }



           return $AVERAGE ;
    }

        private function AverageCostGet($product,$code,$store){


                       $def=StoresDefaultData::orderBy('id','desc')->first();


           if($def->Cost_Price == 1){


                       $lastOperation=ProductMoves::orderBy('id','desc')->where('P_Code',$code)->where('Product',$product)->where('Store',$store)->first();
              $rr=ProductUnits::where('Product',$product)->where('Def',1)->first();

           if(!empty($lastOperation)){

               if($lastOperation->Current == 0){
                        $AVERAGE = ($lastOperation->CostCurrent) /  1 ;
               }else{
                       $AVERAGE = ($lastOperation->CostCurrent) /  ($lastOperation->Current) ;
               }


           }else{
           $AVERAGE = $rr->Price ;
           }



           }elseif($def->Cost_Price == 0){




          $PROO=ProductsPurchases::orderBy('id','desc')->where('Product_Code',$code)->where('Product',$product)->where('Store',$store)->first();
               $PROOStart=ProductsStartPeriods::orderBy('id','desc')->where('P_Code',$code)->where('Product',$product)->where('Store',$store)->first();
                    $rr=ProductUnits::where('Product',$product)->where('Def',1)->first();

                   if(!empty($PROO)){
                         $AVERAGE=$PROO->Price;
                   }else{


                       if(!empty($PROOStart)){

                             $AVERAGE = $PROOStart->Price ;
                       }else{
                          $AVERAGE = $rr->Price ;
                       }


                   }



           }elseif($def->Cost_Price == 2){

                $rr=ProductUnits::where('Product',$product)->where('Def',1)->first();

                    $fifo =FifoQty::orderBy('id','asc')
                 ->where('Store',$store)
              ->where('Product',$product)
    ->where('P_Code',$code)
                ->where('Qty','!=',0)
                ->first();



                  if(empty($fifo)){

  $fifo =FifoQty::orderBy('id','asc')
                  ->where('Store',$store)
                ->where('Product',$product)
                ->where('PP_Code',$code)
                   ->where('Qty','!=',0)
                ->first();

if(empty($fifo)){

  $fifo =FifoQty::orderBy('id','asc')
             ->where('Store',$store)
                ->where('Product',$product)
                ->where('PPP_Code',$code)
                  ->where('Qty','!=',0)
                ->first();


if(empty($fifo)){

  $fifo =FifoQty::orderBy('id','asc')
           ->where('Store',$store)
                ->where('Product',$product)
                ->where('PPPP_Code',$code)
      ->where('Qty','!=',0)
                ->first();

}

}

}


               if(!empty($fifo)){

                   if($fifo->Qty == 0){



         $ty=$this->TestCost($fifo->Store,$fifo->Product,$fifo->P_Code,$fifo->id,$fifo->Purchases_Date);

        $AVERAGE = $ty ;

                   }else{
                  $AVERAGE = $fifo->Cost_Price ;
                   }


               }else{

                 $AVERAGE = $rr->Price ;

               }




           }



          return number_format((float)$AVERAGE, 2, '.', '') ;
    }

    private function TestCost($store,$product,$code,$id,$Purchases_Date){


                   $rr=ProductUnits::where('Product',$product)->where('Def',1)->first();

            $fifo =FifoQty::
                where('Store',$store)
                ->where('Product',$product)
                ->where('P_Code',$code)
                ->where('id','!=',$id)
                     ->where('Purchases_Date','>',$Purchases_Date)
                ->first();

                  if(empty($fifo)){

  $fifo =FifoQty::
                  where('Store',$store)
                ->where('Product',$product)
                ->where('PP_Code',$code)
               ->where('id','!=',$id)
                     ->where('Purchases_Date','>',$Purchases_Date)
                ->first();

if(empty($fifo)){

  $fifo =FifoQty::
             where('Store',$store)
                ->where('Product',$product)
                ->where('PPP_Code',$code)
             ->where('id','!=',$id)
                     ->where('Purchases_Date','>',$Purchases_Date)
                ->first();


if(empty($fifo)){

  $fifo =FifoQty::
           where('Store',$store)
                ->where('Product',$product)
                ->where('PPPP_Code',$code)
               ->where('id','!=',$id)
                     ->where('Purchases_Date','>',$Purchases_Date)
                ->first();

}

}

}

               if(!empty($fifo)){

                   if($fifo->Qty == 0){



         $ty=$this->TestCost($fifo->Store,$fifo->Product,$fifo->P_Code,$fifo->id,$fifo->Purchases_Date);

        $AVERAGE = $ty ;

                   }else{
                  $AVERAGE = $fifo->Cost_Price ;
                   }


               }else{

                 $AVERAGE = $rr->Price ;

               }

        return $AVERAGE ;


    }

     private function AverageCostTwo($TotaNewCost,$newQty,$product,$code,$store){



                             $def=StoresDefaultData::orderBy('id','desc')->first();


           if($def->Cost_Price == 1){


           $lastOperation=ProductMoves::orderBy('id','desc')->where('P_Code',$code)->where('Product',$product)->where('Store',$store)->first();

          $rr=ProductUnits::where('Product',$product)->where('Def',1)->first();
               if(!empty($lastOperation)){
           $AVERAGE = ($lastOperation->CostCurrent) /  ($lastOperation->Current) ;
           }else{
               if(!empty($rr->Price)){
           $AVERAGE = $rr->Price ;
               }else{
            $AVERAGE=1;
               }
           }



   }elseif($def->Cost_Price == 0){



                  $PROO=ProductsPurchases::orderBy('id','desc')->where('Product_Code',$code)->where('Product',$product)->where('Store',$store)->first();
               $PROOStart=ProductsStartPeriods::orderBy('id','desc')->where('P_Code',$code)->where('Product',$product)->where('Store',$store)->first();
                    $rr=ProductUnits::where('Product',$product)->where('Def',1)->first();

                   if(!empty($PROO)){
                         $AVERAGE=$PROO->Price;
                   }else{


                       if(!empty($PROOStart)){

                             $AVERAGE = $PROOStart->Price ;
                       }else{
                          $AVERAGE = $rr->Price ;
                       }


                   }



           }elseif($def->Cost_Price == 2){

                $rr=ProductUnits::where('Product',$product)->where('Def',1)->first();

                   $fifo =FifoQty::orderBy('id','asc')
                 ->where('Store',$store)
              ->where('Product',$product)
    ->where('P_Code',$code)
                ->where('Qty','!=',0)
                ->first();



                  if(empty($fifo)){

  $fifo =FifoQty::orderBy('id','asc')
                  ->where('Store',$store)
                ->where('Product',$product)
                ->where('PP_Code',$code)
                   ->where('Qty','!=',0)
                ->first();

if(empty($fifo)){

  $fifo =FifoQty::orderBy('id','asc')
             ->where('Store',$store)
                ->where('Product',$product)
                ->where('PPP_Code',$code)
                  ->where('Qty','!=',0)
                ->first();


if(empty($fifo)){

  $fifo =FifoQty::orderBy('id','asc')
           ->where('Store',$store)
                ->where('Product',$product)
                ->where('PPPP_Code',$code)
      ->where('Qty','!=',0)
                ->first();

}

}

}


               if(!empty($fifo)){

                   if($fifo->Qty == 0){



         $ty=$this->TestCost($fifo->Store,$fifo->Product,$fifo->P_Code,$fifo->id,$fifo->Purchases_Date);

        $AVERAGE = $ty ;

                   }else{
                  $AVERAGE = $fifo->Cost_Price ;
                   }


               }else{

                 $AVERAGE = $rr->Price ;

               }




           }


           return $AVERAGE ;
    }


    //Fifo
          private function FifoStoreQty($store,$product,$code,$id,$Purchases_Date,$Qty,$Unit,$SalesID){

               $fifo =FifoQty::
                where('Store',$store)
                ->where('Product',$product)
                ->where('P_Code',$code)
                ->where('id','!=',$id)
                     ->where('Purchases_Date','>',$Purchases_Date)
                ->first();

                  if(empty($fifo)){

  $fifo =FifoQty::
                  where('Store',$store)
                ->where('Product',$product)
                ->where('PP_Code',$code)
               ->where('id','!=',$id)
                     ->where('Purchases_Date','>',$Purchases_Date)
                ->first();

if(empty($fifo)){

  $fifo =FifoQty::
             where('Store',$store)
                ->where('Product',$product)
                ->where('PPP_Code',$code)
             ->where('id','!=',$id)
                     ->where('Purchases_Date','>',$Purchases_Date)
                ->first();


if(empty($fifo)){

  $fifo =FifoQty::
           where('Store',$store)
                ->where('Product',$product)
                ->where('PPPP_Code',$code)
               ->where('id','!=',$id)
                     ->where('Purchases_Date','>',$Purchases_Date)
                ->first();

}

}

}



    if(!empty($fifo)){

        if($fifo->Qty >= $Qty){


                   $unit=ProductUnits::where('Unit',$Unit)->where('Product',$product)->first();

           $qq= $unit->Rate * $Qty ;

           $newqty=$fifo->Qty -  $qq ;

               FifoQty::where('id',$fifo->id)->update(['Qty'=>$newqty]);


                 $fifQty['Sales_Qty']=$Qty;
            $fifQty['Sales_ID']=$SalesID;
            $fifQty['Fifo_ID']=$fifo->id;
            $fifQty['Store']=$fifo->Store;
            $fifQty['Product']=$fifo->Product;
            $fifQty['Cost_Price']=$fifo->Cost_Price;
            $fifQty['Purchases_Date']=$fifo->Purchases_Date;

            SalesFifoQty::create($fifQty);




        }else{


        $resdiualQty=$Qty - $fifo->Qty ;

         $unit=ProductUnits::where('Unit',$Unit)->where('Product',$product)->first();

           $qq= $unit->Rate * $fifo->Qty ;

           $newqty=$fifo->Qty -  $qq ;

               FifoQty::where('id',$fifo->id)->update(['Qty'=>$newqty]);


                 $fifQty['Sales_Qty']=$fifo->Qty;
            $fifQty['Sales_ID']=$SalesID;
            $fifQty['Fifo_ID']=$fifo->id;
            $fifQty['Store']=$fifo->Store;
            $fifQty['Product']=$fifo->Product;
            $fifQty['Cost_Price']=$fifo->Cost_Price;
            $fifQty['Purchases_Date']=$fifo->Purchases_Date;

            SalesFifoQty::create($fifQty);





     $ResdiualCost=$this->FifoStoreQty($fifo->Store,$fifo->Product,$fifo->P_Code,$fifo->id,$fifo->Purchases_Date,$resdiualQty,$Unit,$SalesID);





        }


    }



        return  $newqty  ;


    }




          //======  Webslider =======
    public function WebSliderPage(){
        $items=Webslider::all();
         return view('admin.Website.Webslider',['items'=>$items]);
    }

     public function AddWebSlider(){

        $data= $this->validate(request(),[
             'Arabic_Title'=>'required',
             'Arabic_Desc'=>'required',
             'English_Desc'=>'required',
             'Image'=>'required|max:100000',
               ],[
            'Arabic_Title.required' => trans('admin.Arabic_TitleRequired'),
            'English_Title.required' => trans('admin.English_TitleRequired'),
            'Image.required' => trans('admin.ImageRequired'),

         ]);

            $image=request()->file('Image');
          if($image){
            $image_name=Str::random(20);
            $ext=strtolower($image->getClientOriginalExtension());
            $image_full_name=$image_name .'.' . $ext ;
            $upload_path='WebsliderImages/';
            $image_url=$upload_path.$image_full_name;
            $success=$image->move($upload_path,$image_full_name);
                   }


         $data['Image']=$image_url;
         $data['Arabic_Title']=request('Arabic_Title');
         $data['Type']=request('Type');
         $data['Status']=1;
         $data['Arabic_Desc']=request('Arabic_Desc');
         $data['English_Desc']=request('English_Desc');
         if(!empty(request('English_Title'))){
         $data['English_Title']=request('English_Title');
         }else{
           $data['English_Title']=request('Arabic_Title');
         }

         Webslider::create($data);

           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
           $dataUser['Screen']='ويب سلايدر';
           $dataUser['ScreenEn']='WebSlider';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=request('Arabic_Title');
           $dataUser['ExplainEn']=request('English_Title');
           UsersMoves::create($dataUser);

             session()->flash('success',trans('admin.Added_Successfully'));
             return back();

    }

     public function EditWebSlider($id){

              $data= $this->validate(request(),[
             'Arabic_Title'=>'required',
             'Image'=>'sometimes|nullable|max:100000',
               ],[
            'Arabic_Title.required' => trans('admin.Arabic_TitleRequired'),
            'English_Title.required' => trans('admin.English_TitleRequired'),
            'Image.required' => trans('admin.ImageRequired'),

         ]);



            $image=request()->file('Image');
          if($image){
            $image_name=Str::random(20);
            $ext=strtolower($image->getClientOriginalExtension());
            $image_full_name=$image_name .'.' . $ext ;
            $upload_path='WebsliderImages/';
            $image_url=$upload_path.$image_full_name;
            $success=$image->move($upload_path,$image_full_name);
                   }

         if(!empty($image_url)){
            $data['Image']=$image_url;
         }else{

             $data['Image']=request('Images');
         }

              $data['Status']=request('Status');
         $data['Arabic_Desc']=request('Arabic_Desc');
         $data['English_Desc']=request('English_Desc');
         $data['Arabic_Title']=request('Arabic_Title');
           $data['Type']=request('Type');
         if(!empty(request('English_Title'))){
         $data['English_Title']=request('English_Title');
         }else{
           $data['English_Title']=request('Arabic_Title');
         }

           Webslider::where('id',$id)->update($data);

           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
              $dataUser['Screen']='ويب سلايدر';
           $dataUser['ScreenEn']='WebSlider';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
           $dataUser['Explain']=request('Arabic_Title');
           $dataUser['ExplainEn']=request('English_Title');
           UsersMoves::create($dataUser);


            session()->flash('success',trans('admin.Updated'));
            return back();


     }

     public function DeleteWebSlider($id){

        $del=Webslider::find($id);

         $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                    $dataUser['Screen']='ويب سلايدر';
           $dataUser['ScreenEn']='WebSlider';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';

            $dataUser['Explain']=$del->Arabic_Title;
            $dataUser['ExplainEn']=$del->English_Title;
           UsersMoves::create($dataUser);

        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }

     public function UnActiveSlider($id){

        Webslider::where('id',$id)->update(['Status'=>0]);
        session()->flash('error',trans('admin.UnActived'));
        return back();

           }

         public function ActiveSlider($id){

        Webslider::where('id',$id)->update(['Status'=>1]);
        session()->flash('success',trans('admin.Actived'));
        return back();

           }


             //======  About =======
    public function AboutPage(){
        $item=About::orderBy('id','desc')->first();
         return view('admin.Website.About',['item'=>$item]);
    }

     public function UpdateAbout($id){

              $data= $this->validate(request(),[
             'Arabic_Desc'=>'required',
             'English_Desc'=>'required',
             'Arabic_Title'=>'required',
             'English_Title'=>'required',
             'Image'=>'sometimes|nullable|max:100000',
             'Image_2'=>'sometimes|nullable|max:100000',
             'Image_3'=>'sometimes|nullable|max:100000',

               ],[
            'Arabic_Desc.required' => trans('admin.Arabic_DescRequired'),
            'English_Desc.required' => trans('admin.English_DescRequired'),
            'Arabic_Title.required' => trans('admin.Arabic_TitleRequired'),
            'English_Title.required' => trans('admin.English_TitleRequired'),
            'Image.required' => trans('admin.ImageRequired'),


         ]);




            $image=request()->file('Image');
          if($image){
            $image_name=Str::random(20);
            $ext=strtolower($image->getClientOriginalExtension());
            $image_full_name=$image_name .'.' . $ext ;
            $upload_path='AboutImages/';
            $image_url=$upload_path.$image_full_name;
            $success=$image->move($upload_path,$image_full_name);
                   }
         if(!empty($image_url)){
            $data['Image']=$image_url;
         }else{

             $data['Image']=request('Images');
         }


               $imagex=request()->file('Image_2');
          if($imagex){
            $image_namex=Str::random(20);
            $extx=strtolower($imagex->getClientOriginalExtension());
            $image_full_namex=$image_namex .'.' . $extx ;
            $upload_pathx='AboutImages/';
            $image_urlx=$upload_pathx.$image_full_namex;
            $successx=$imagex->move($upload_pathx,$image_full_namex);
                   }
         if(!empty($image_urlx)){
            $data['Image_2']=$image_urlx;
         }else{

             $data['Image_2']=request('Image_2');
         }

                      $imagexe=request()->file('Image_3');
          if($imagexe){
            $image_namexe=Str::random(20);
            $extxe=strtolower($imagexe->getClientOriginalExtension());
            $image_full_namexe=$image_namexe .'.' . $extxe ;
            $upload_pathxe='AboutImages/';
            $image_urlxe=$upload_pathxe.$image_full_namexe;
            $successxe=$imagexe->move($upload_pathxe,$image_full_namexe);
                   }
         if(!empty($image_urlxe)){
            $data['Image_3']=$image_urlxe;
         }else{

             $data['Image_3']=request('Image_3');
         }

         $data['Arabic_Desc']=request('Arabic_Desc');
         $data['English_Desc']=request('English_Desc');
         $data['Arabic_Title']=request('Arabic_Title');
         $data['English_Title']=request('English_Title');


           About::where('id',$id)->update($data);

           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                  $dataUser['Screen']='من نحن';
           $dataUser['ScreenEn']='About';
           $dataUser['Type']='تحديث';
           $dataUser['TypeEn']='Update';
           $dataUser['Explain']=$id;
           $dataUser['ExplainEn']=$id;
           UsersMoves::create($dataUser);


            session()->flash('success',trans('admin.Updated'));
            return back();


     }


             // ==== Social Media ==
            public function SocialMediaPage(){

                 $item=SocialMedia::orderBy('id','desc')->first();
        return view('admin.Website.SocialMedia',["item"=>$item]);

    }

            public function SocialMediaUpdate($id){

          $data['Facebook'] = request('Facebook');
          $data['Twitter'] = request('Twitter');
          $data['Instagram'] = request('Instagram');
          $data['Youtube'] = request('Youtube');
          $data['Snapchat'] = request('Snapchat');
          $data['Whatsapp'] = request('Whatsapp');
          $data['Google_Plus'] = request('Google_Plus');
          $data['LinkedIn'] = request('LinkedIn');
          $data['Pinterest'] = request('Pinterest');
          $data['Telegram'] = request('Telegram');
          $data['iOS'] = request('iOS');
          $data['Android'] = request('Android');


           SocialMedia::OrderBy('id','desc')->update($data);
                session()->flash('success',trans('admin.Updated'));
                return redirect('SocialMedia');



      }


             //======  MsgRqst =======
    public function MsgRqstPage(){
        $items=MsgRqst::paginate(100);
         return view('admin.Website.MsgRqst',['items'=>$items]);
    }

     public function DeleteMsgRqst($id){

        $del=MsgRqst::find($id);

         $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                  $dataUser['Screen']='طلبات الرسائل';
           $dataUser['ScreenEn']='Message Request';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';
           $dataUser['Explain']=$id;
           $dataUser['ExplainEn']=$id;

           UsersMoves::create($dataUser);

        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }


              //======  ContactUS =======
    public function ContactUSPage(){
        $item=ContactUS::orderBy('id','desc')->first();
         return view('admin.Website.ContactUS',['item'=>$item]);
    }

     public function ContactUSUpdate($id){

              $data= $this->validate(request(),[
             'Map'=>'required',
             'Arabic_Title'=>'required',
             'English_Title'=>'required',
             'Arabic_Desc'=>'required',
             'English_Desc'=>'required',
             'Opening_Hours'=>'required',
             'Phone1'=>'required',
             'Phone2'=>'required',
             'Phone_Header'=>'required',
             'Email'=>'required',
             'Arabic_Address'=>'required',
             'English_Address'=>'required',


               ],[
         ]);


         $data['Map']=request('Map');
         $data['Arabic_Title']=request('Arabic_Title');
         $data['English_Title']=request('English_Title');
         $data['Arabic_Desc']=request('Arabic_Desc');
         $data['English_Desc']=request('English_Desc');
         $data['Opening_Hours']=request('Opening_Hours');
         $data['Phone1']=request('Phone1');
         $data['Phone2']=request('Phone2');
         $data['Phone_Header']=request('Phone_Header');
         $data['Email']=request('Email');
         $data['Arabic_Address']=request('Arabic_Address');
         $data['English_Address']=request('English_Address');


           ContactUS::where('id',$id)->update($data);

           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                   $dataUser['Screen']='التواصل';
           $dataUser['ScreenEn']='Contact';
           $dataUser['Type']='تحديث';
           $dataUser['TypeEn']='Update';
           $dataUser['Explain']=$id;
           $dataUser['ExplainEn']=$id;
           UsersMoves::create($dataUser);


            session()->flash('success',trans('admin.Updated'));
            return back();


     }


              //======  Articles =======
    public function ArticlesPage(){
        $items=Articles::all();
         return view('admin.Website.Articles',['items'=>$items]);
    }

     public function AddArticles(){

        $data= $this->validate(request(),[
             'Sub_Image'=>'required',
             'Arabic_Title'=>'required',
             'English_Title'=>'required',
             'Arabic_Desc'=>'required',
             'English_Desc'=>'required',
             'Date'=>'required',
             'Image'=>'required|max:100000',
               ],[
            'Arabic_Desc.required' => trans('admin.Arabic_DescRequired'),
            'English_Desc.required' => trans('admin.English_DescRequired'),
            'Arabic_Title.required' => trans('admin.Arabic_TitleRequired'),
            'English_Title.required' => trans('admin.English_TitleRequired'),
            'Image.required' => trans('admin.ImageRequired'),
            'Sub_Image.required' => trans('admin.ImageRequired'),

         ]);

            $image=request()->file('Image');
          if($image){
            $image_name=Str::random(20);
            $ext=strtolower($image->getClientOriginalExtension());
            $image_full_name=$image_name .'.' . $ext ;
            $upload_path='ArticlesImages/';
            $image_url=$upload_path.$image_full_name;
            $success=$image->move($upload_path,$image_full_name);
                   }

         $data['Image']=$image_url;


           $imagee=request()->file('Sub_Image');
          if($imagee){
            $image_namee=Str::random(20);
            $exte=strtolower($imagee->getClientOriginalExtension());
            $image_full_namee=$image_namee .'.' . $exte ;
            $upload_pathe='ArticlesImages/';
            $image_urle=$upload_pathe.$image_full_namee;
            $successe=$imagee->move($upload_pathe,$image_full_namee);
                   }

         $data['Sub_Image']=$image_urle;
         $data['Arabic_Title']=request('Arabic_Title');
         $data['English_Title']=request('English_Title');
         $data['Arabic_Desc']=request('Arabic_Desc');
         $data['English_Desc']=request('English_Desc');
         $data['Date']=request('Date');


         Articles::create($data);

           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                   $dataUser['Screen']='المقالات';
           $dataUser['ScreenEn']='Blogs';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=request('Arabic_Title');
           $dataUser['ExplainEn']=request('English_Title');
           UsersMoves::create($dataUser);

             session()->flash('success',trans('admin.Added_Successfully'));
             return back();

    }

     public function EditArticles($id){

        $data= $this->validate(request(),[
             'Sub_Image'=>'sometimes|nullable|max:100000',
             'Arabic_Title'=>'required',
             'English_Title'=>'required',
             'Arabic_Desc'=>'required',
             'English_Desc'=>'required',
             'Date'=>'required',
             'Image'=>'sometimes|nullable|max:100000',
               ],[
            'Arabic_Desc.required' => trans('admin.Arabic_DescRequired'),
            'English_Desc.required' => trans('admin.English_DescRequired'),
            'Arabic_Title.required' => trans('admin.Arabic_TitleRequired'),
            'English_Title.required' => trans('admin.English_TitleRequired'),
            'Image.required' => trans('admin.ImageRequired'),
            'Sub_Image.required' => trans('admin.ImageRequired'),

         ]);

            $image=request()->file('Image');
          if($image){
            $image_name=Str::random(20);
            $ext=strtolower($image->getClientOriginalExtension());
            $image_full_name=$image_name .'.' . $ext ;
            $upload_path='WebsliderImages/';
            $image_url=$upload_path.$image_full_name;
            $success=$image->move($upload_path,$image_full_name);
                   }

         if(!empty($image_url)){
            $data['Image']=$image_url;
         }else{

             $data['Image']=request('Images');
         }


                    $imagee=request()->file('Sub_Image');
          if($imagee){
            $image_namee=Str::random(20);
            $exte=strtolower($imagee->getClientOriginalExtension());
            $image_full_namee=$image_namee .'.' . $exte ;
            $upload_pathe='ArticlesImages/';
            $image_urle=$upload_pathe.$image_full_namee;
            $successe=$imagee->move($upload_pathe,$image_full_namee);
                   }

                 if(!empty($image_urle)){
            $data['Sub_Image']=$image_urle;
         }else{

             $data['Sub_Image']=request('Sub_Images');
         }


        $data['Arabic_Title']=request('Arabic_Title');
         $data['English_Title']=request('English_Title');
         $data['Arabic_Desc']=request('Arabic_Desc');
         $data['English_Desc']=request('English_Desc');
         $data['Date']=request('Date');

           Articles::where('id',$id)->update($data);

           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                        $dataUser['Screen']='المقالات';
           $dataUser['ScreenEn']='Blogs';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
           $dataUser['Explain']=request('Arabic_Title');
           $dataUser['ExplainEn']=request('English_Title');
           UsersMoves::create($dataUser);


            session()->flash('success',trans('admin.Updated'));
            return back();


     }

     public function DeleteArticles($id){

        $del=Articles::find($id);

         $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                         $dataUser['Screen']='المقالات';
           $dataUser['ScreenEn']='Blogs';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';
            $dataUser['Explain']=$del->Arabic_Title;
            $dataUser['ExplainEn']=$del->English_Title;
           UsersMoves::create($dataUser);

        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }


            //======   Terms =======
    public function TermsPage(){
        $item=Terms::orderBy('id','desc')->first();
         return view('admin.Website.Terms',['item'=>$item]);
    }

     public function UpdateTerms($id){

              $data= $this->validate(request(),[
             'Arabic_Desc'=>'required',
             'English_Desc'=>'required',
               ],[
            'Arabic_Desc.required' => trans('admin.Arabic_DescRequired'),
            'English_Desc.required' => trans('admin.English_DescRequired'),
         ]);

         $data['Arabic_Desc']=request('Arabic_Desc');
         $data['English_Desc']=request('English_Desc');

           Terms::where('id',$id)->update($data);

           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                  $dataUser['Screen']='الشروط و الاحكام';
           $dataUser['ScreenEn']='Terms and Conditions';
           $dataUser['Type']='تحديث';
           $dataUser['TypeEn']='Update';
           $dataUser['Explain']=$id;
           $dataUser['ExplainEn']=$id;
           UsersMoves::create($dataUser);


            session()->flash('success',trans('admin.Updated'));
            return back();


     }


        //======   Polices =======
    public function PolicesPage(){
        $item=Polices::orderBy('id','desc')->first();
         return view('admin.Website.Polices',['item'=>$item]);
    }

     public function UpdatePolices($id){

                      $data= $this->validate(request(),[
             'Arabic_Desc'=>'required',
             'English_Desc'=>'required',
               ],[
            'Arabic_Desc.required' => trans('admin.Arabic_DescRequired'),
            'English_Desc.required' => trans('admin.English_DescRequired'),
         ]);

         $data['Arabic_Desc']=request('Arabic_Desc');
         $data['English_Desc']=request('English_Desc');

           Polices::where('id',$id)->update($data);

           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                $dataUser['Screen']='السياسات';
           $dataUser['ScreenEn']='Polices';
           $dataUser['Type']='تحديث';
           $dataUser['TypeEn']='Update';
           $dataUser['Explain']=$id;
           $dataUser['ExplainEn']=$id;
           UsersMoves::create($dataUser);


            session()->flash('success',trans('admin.Updated'));
            return back();


     }

        //Coupon Code

      public function  CouponCodePage(){
       $items=CouponCode::all();
        return view('admin.Website.CouponCode',["items"=>$items]);
    }

      public function AddCouponCode(){

             $data= $this->validate(request(),[
                 'Amount'=>'required',
                 'Num'=>'required',
                 'Expire'=>'required',
                  'Code'=>'required|unique:coupon_codes',


                    ],[
                'Code.required' =>trans('admin.CodeRequired'),
                'Amount.required' =>trans('admin.AmountRequired'),
                'Num.required' =>trans('admin.NumRequired'),
                'Expire.required' =>trans('admin.ExpireRequired'),

             ]);

                      $data['Code']=request('Code');
                      $data['Amount']=request('Amount');
                      $data['Status']=0;
                      $data['Used']=0;
                      $data['Num']=request('Num');
                      $data['Expire']=request('Expire');


                        CouponCode::create($data);

                      session()->flash('success',trans('admin.Added_Successfully'));
                      return back();
}

      public function EditCouponCode($id){
            $data= $this->validate(request(),[
                'Code'=>'required|unique:coupon_codes,Code,'.$id,
                 'Amount'=>'required',
                 'Num'=>'required',
                 'Expire'=>'required',

                    ],[
                'Code.required' =>trans('admin.CodeRequired'),
                'Amount.required' =>trans('admin.AmountRequired'),
                'Num.required' =>trans('admin.NumRequired'),
                'Expire.required' =>trans('admin.ExpireRequired'),

             ]);

                      $data['Code']=request('Code');
                      $data['Amount']=request('Amount');
                      $data['Status']=request('Status');
                      $data['Num']=request('Num');
                      $data['Expire']=request('Expire');


                CouponCode::where('id',$id)->update($data);
                session()->flash('success',trans('admin.Updated'));
                return back();

}

      public function DeleteCouponCode($id){

            $del=CouponCode::find($id);
            $del->delete();
            session()->flash('error',trans('admin.Deleted'));
            return back();

}



                 //======  FAQ =======
    public function FAQPage(){
        $items=FAQ::all();
         return view('admin.Website.FAQ',['items'=>$items]);
    }

     public function AddFAQ(){

        $data= $this->validate(request(),[
             'Arabic_Question'=>'required',
             'English_Question'=>'required',
             'Arabic_Answer'=>'required',
             'English_Answer'=>'required',


               ],[


         ]);


         $data['Arabic_Question']=request('Arabic_Question');
         $data['English_Question']=request('English_Question');
         $data['Arabic_Answer']=request('Arabic_Answer');
         $data['English_Answer']=request('English_Answer');



         FAQ::create($data);

           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                    $dataUser['Screen']='الاسئله الشائعه';
           $dataUser['ScreenEn']='FAQ';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=request('Arabic_Question');
           $dataUser['ExplainEn']=request('English_Question');
           UsersMoves::create($dataUser);

             session()->flash('success',trans('admin.Added_Successfully'));
             return back();

    }

     public function EditFAQ($id){

              $data= $this->validate(request(),[
             'Arabic_Question'=>'required',
             'English_Question'=>'required',
             'Arabic_Answer'=>'required',
             'English_Answer'=>'required',


               ],[


         ]);


         $data['Arabic_Question']=request('Arabic_Question');
         $data['English_Question']=request('English_Question');
         $data['Arabic_Answer']=request('Arabic_Answer');
         $data['English_Answer']=request('English_Answer');

           FAQ::where('id',$id)->update($data);

           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                        $dataUser['Screen']='الاسئله الشائعه';
           $dataUser['ScreenEn']='FAQ';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
           $dataUser['Explain']=request('Arabic_Question');
           $dataUser['ExplainEn']=request('English_Question');
           UsersMoves::create($dataUser);


            session()->flash('success',trans('admin.Updated'));
            return back();


     }

     public function DeleteFAQ($id){

        $del=FAQ::find($id);

         $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                          $dataUser['Screen']='الاسئله الشائعه';
           $dataUser['ScreenEn']='FAQ';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';
            $dataUser['Explain']=$del->Arabic_Question;
            $dataUser['ExplainEn']=$del->English_Question;
           UsersMoves::create($dataUser);

        $del->delete();
        session()->flash('error',trans('admin.Deleted'));
        return back();

           }


                   //======  Countris =======
    public function CountrisPage(){
        $items=Countris::all();
        $Coins=Coins::all();
        $Stores=Stores::all();
             $Safes = AcccountingManual::
             where('Type',1)
              ->where('Parent',28)
              ->orWhere('Parent',29)
              ->get();
         return view('admin.Website.Countris',['items'=>$items,'Safes'=>$Safes,'Coins'=>$Coins,'Stores'=>$Stores]);
    }

     public function AddCountris(){

        $data= $this->validate(request(),[
             'Arabic_Name'=>'required',
             'English_Name'=>'required',
             'Flag'=>'required',
             'Safe'=>'required',


               ],[


         ]);





               $image=request()->file('Flag');
          if($image){
            $image_name=Str::random(20);
            $ext=strtolower($image->getClientOriginalExtension());
            $image_full_name=$image_name .'.' . $ext ;
            $upload_path='CountriesImages/';
            $image_url=$upload_path.$image_full_name;
            $success=$image->move($upload_path,$image_full_name);
                   }

         $data['Flag']=$image_url;

         $data['Arabic_Name']=request('Arabic_Name');
         $data['English_Name']=request('English_Name');

         $data['Safe']=request('Safe');
         $data['Store']=request('Store');
         $data['Coin']=request('Coin');
         $data['Code']=request('Code');
         $data['SearchCode']=request('SearchCode');

         Countris::create($data);

           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
           $dataUser['Screen']='الدول';
           $dataUser['ScreenEn']='Countris';
           $dataUser['Type']='اضافه جديده';
           $dataUser['TypeEn']='New Add';
           $dataUser['Explain']=request('Arabic_Name');
           $dataUser['ExplainEn']=request('English_Name');
           UsersMoves::create($dataUser);

             session()->flash('success',trans('admin.Added_Successfully'));
             return back();

    }

     public function EditCountris($id){

  $data= $this->validate(request(),[
             'Arabic_Name'=>'required',
             'English_Name'=>'required',

             'Safe'=>'required',


               ],[


         ]);





               $image=request()->file('Flag');
          if($image){
            $image_name=Str::random(20);
            $ext=strtolower($image->getClientOriginalExtension());
            $image_full_name=$image_name .'.' . $ext ;
            $upload_path='CountriesImages/';
            $image_url=$upload_path.$image_full_name;
            $success=$image->move($upload_path,$image_full_name);
                   }


         if(!empty($image_url)){
           $data['Flag']=$image_url;
         }else{
            $data['Flag']=request('Flags');
         }


         $data['Arabic_Name']=request('Arabic_Name');
         $data['English_Name']=request('English_Name');

         $data['Safe']=request('Safe');
  $data['Coin']=request('Coin');
  $data['Store']=request('Store');
  $data['Code']=request('Code');
  $data['SearchCode']=request('SearchCode');

           Countris::where('id',$id)->update($data);

           $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                   $dataUser['Screen']='الدول';
           $dataUser['ScreenEn']='Countris';
           $dataUser['Type']='تعديل';
           $dataUser['TypeEn']='Edit';
           $dataUser['Explain']=request('Arabic_Name');
           $dataUser['ExplainEn']=request('English_Name');
           UsersMoves::create($dataUser);


            session()->flash('success',trans('admin.Updated'));
            return back();


     }

     public function DeleteCountris($id){

         $Count=Governrate::where('Country',$id)->count();

         if($Count == 0){
        $del=Countris::find($id);

         $dataUser['User']=auth()->guard('admin')->user()->id;
           $dataUser['Date']=date('Y-m-d');
           $dataUser['Time']=date("h:i:s a", time());
                   $dataUser['Screen']='الدول';
           $dataUser['ScreenEn']='Countris';
           $dataUser['Type']='حذف';
           $dataUser['TypeEn']='Delete';
            $dataUser['Explain']=$del->Arabic_Name;
            $dataUser['ExplainEn']=$del->English_Name;
           UsersMoves::create($dataUser);

        $del->delete();
                  session()->flash('error',trans('admin.Deleted'));
        return back();
         }else{
                 session()->flash('error',trans('admin.CantDeleteThisItem'));
        return back();

         }


           }


                     //======  ProDetailsImg =======
    public function ProDetailsImg(){
        $items=ProDetailsImg::all();
         return view('admin.Website.ProDetailsImg',['items'=>$items]);
    }

     public function EditProDetailsImg($id){

        $data= $this->validate(request(),[
             'Image'=>'sometimes|nullable|max:100000',

               ],[
            'Arabic_Desc.required' => trans('admin.Arabic_DescRequired'),
            'English_Desc.required' => trans('admin.English_DescRequired'),
            'Arabic_Title.required' => trans('admin.Arabic_TitleRequired'),
            'English_Title.required' => trans('admin.English_TitleRequired'),


         ]);

            $image=request()->file('Image');
          if($image){
            $image_name=Str::random(20);
            $ext=strtolower($image->getClientOriginalExtension());
            $image_full_name=$image_name .'.' . $ext ;
            $upload_path='WebsliderImages/';
            $image_url=$upload_path.$image_full_name;
            $success=$image->move($upload_path,$image_full_name);
                   }

         if(!empty($image_url)){
            $data['Image']=$image_url;
         }else{

             $data['Image']=request('Images');
         }


        $data['Arabic_Title']=request('Arabic_Title');
         $data['English_Title']=request('English_Title');
         $data['Arabic_Desc']=request('Arabic_Desc');
         $data['English_Desc']=request('English_Desc');


           ProDetailsImg::where('id',$id)->update($data);



            session()->flash('success',trans('admin.Updated'));
            return back();


     }


                         //======  BefroeFooter =======
    public function BefroeFooter(){
        $items=BefroeFooter::all();
         return view('admin.Website.BefroeFooter',['items'=>$items]);
    }

     public function EditBefroeFooter($id){

        $data= $this->validate(request(),[
             'Image'=>'sometimes|nullable|max:100000',
             'Arabic_Title'=>'required',
             'English_Title'=>'required',


               ],[
            'Arabic_Desc.required' => trans('admin.Arabic_DescRequired'),
            'English_Desc.required' => trans('admin.English_DescRequired'),
            'Arabic_Title.required' => trans('admin.Arabic_TitleRequired'),
            'English_Title.required' => trans('admin.English_TitleRequired'),


         ]);


        $data['Image']=request('Image');
        $data['Arabic_Title']=request('Arabic_Title');
         $data['English_Title']=request('English_Title');
         $data['Arabic_Desc']=request('Arabic_Desc');
         $data['English_Desc']=request('English_Desc');


           BefroeFooter::where('id',$id)->update($data);


            session()->flash('success',trans('admin.Updated'));
            return back();


     }


    //===  ShopOrders ==
     public function ShopOrders(){
            $items=SalesOrder::where('Order_Type',1)->orderBy('id','desc')->paginate(100);

         return view('admin.Website.ShopOrders',['items'=>$items]);
    }

     public function ChangeStatusShop(){

 SalesOrder::where('id',request('ID'))->update(['Delivery_Status'=>request('Delivery_Status')]);


        session()->flash('error',trans('admin.Updated'));
        return back();

           }



    //EComDesign
      public function EComDesign(){
            $Main=MainEComDesign::orderBy('id','desc')->first();
            $Home=HomeEComDesign::orderBy('id','desc')->first();

            $HomePro=HomeProductEComDesign::orderBy('id','desc')->first();
            $SubOne=SupPagesEComDesign::orderBy('id','desc')->first();
            $SubTwo=SupPagesPartTwoEComDesign::orderBy('id','desc')->first();
            $Wish=SupPagesWishCompEComDesign::orderBy('id','desc')->first();
            $ProDetails=ProductDetailsEComDesign::orderBy('id','desc')->first();

         return view('admin.Website.EComDesign',[
             'Main'=>$Main,
             'Home'=>$Home,
             'HomePro'=>$HomePro,
             'SubOne'=>$SubOne,
             'SubTwo'=>$SubTwo,
             'Wish'=>$Wish,
             'ProDetails'=>$ProDetails,

         ]);
    }

   public function AddMainEComDesignFirst(){


         $data['Pagination_BG_Color']=null;

         MainEComDesign::create($data);


             return back();

    }

      public function AddMainEComDesign(){



          $image=request()->file('Body_BG_Image');
          if($image){
            $image_name=Str::random(20);
            $ext=strtolower($image->getClientOriginalExtension());
            $image_full_name=$image_name .'.' . $ext ;
            $upload_path='LogoImages/';
            $image_url=$upload_path.$image_full_name;
            $success=$image->move($upload_path,$image_full_name);
                   }


             if(!empty($image_url)){

                 $data['Body_BG_Image']=$image_url;

             }else{
                 $data['Body_BG_Image']=request('Images');
             }



         $data['Font_Type']=request('Font_Type');
         $data['Pagination_BG_Color']=request('Pagination_BG_Color');
         $data['Pagination_Txt_Color']=request('Pagination_Txt_Color');
         $data['Pagination_Active_BG_Color']=request('Pagination_Active_BG_Color');
         $data['Pagination_Active_Txt_Color']=request('Pagination_Active_Txt_Color');
         $data['Body_BG_Type']=request('Body_BG_Type');
         $data['Body_BG_Color']=request('Body_BG_Color');
         $data['Sub_Page_BG_Color']=request('Sub_Page_BG_Color');
         $data['Breadcumb_BG_Color']=request('Breadcumb_BG_Color');
             $data['Breadcumb_Txt_Color']=request('Breadcumb_Txt_Color');
         $data['Modal_BG_Color']=request('Modal_BG_Color');
         $data['Modal_Txt_Color']=request('Modal_Txt_Color');
         $data['Modal_Button_BG_Color']=request('Modal_Button_BG_Color');
         $data['Modal_Button_Txt_Color']=request('Modal_Button_Txt_Color');
         $data['Table_Header_BG_Color']=request('Table_Header_BG_Color');
         $data['Table_Header_Txt_Color']=request('Table_Header_Txt_Color');
         $data['Table_Body_BG_Color']=request('Table_Body_BG_Color');
         $data['Table_Body_Txt_Color']=request('Table_Body_Txt_Color');
                 $data['Table_Button_BG_Color']=request('Table_Button_BG_Color');
         $data['Table_Button_Txt_Color']=request('Table_Button_Txt_Color');
         $data['CopyRights_Txt_Color']=request('CopyRights_Txt_Color');
         $data['CopyRights_Klar_Txt_Color']=request('CopyRights_Klar_Txt_Color');
         $data['CopyRights_Klar_Hover_Txt_Color']=request('CopyRights_Klar_Hover_Txt_Color');
         $data['Preloader_BG_Color']=request('Preloader_BG_Color');
         $data['Preloader_Small_Circle_Color']=request('Preloader_Small_Circle_Color');
         $data['Preloader_Large_Circle_Color']=request('Preloader_Large_Circle_Color');
         $data['Footer_Title_Color']=request('Footer_Title_Color');
         $data['Footer_Txt_Color']=request('Footer_Txt_Color');
             $data['Footer_Social_Color']=request('Footer_Social_Color');
         $data['Footer_Social_Hover_BG_Color']=request('Footer_Social_Hover_BG_Color');
         $data['Footer_Social_Hover_Txt_Color']=request('Footer_Social_Hover_Txt_Color');
         $data['Header_Top_BG_Color']=request('Header_Top_BG_Color');
         $data['Header_Top_Txt_Color']=request('Header_Top_Txt_Color');
         $data['Header_Top_Txt_Hover_Color']=request('Header_Top_Txt_Hover_Color');
         $data['Header_Middle_BG_Color']=request('Header_Middle_BG_Color');
         $data['Header_Middle_Icon_Color']=request('Header_Middle_Icon_Color');
         $data['Header_Middle_Icon_Hover_Color']=request('Header_Middle_Icon_Hover_Color');
         $data['Header_SearchBar_BG_Color']=request('Header_SearchBar_BG_Color');
            $data['Header_SearchBar_Txt_Color']=request('Header_SearchBar_Txt_Color');
         $data['Header_SearchBar_Icon_BG_Color']=request('Header_SearchBar_Icon_BG_Color');
         $data['Header_SearchBar_Icon_Txt_Color']=request('Header_SearchBar_Icon_Txt_Color');
         $data['Header_SearchBar_Icon_Hover_BG_Color']=request('Header_SearchBar_Icon_Hover_BG_Color');
         $data['Header_SearchBar_Icon_Hover_Txt_Color']=request('Header_SearchBar_Icon_Hover_Txt_Color');
         $data['Navbar_BG_Color']=request('Navbar_BG_Color');
         $data['Navbar_Txt_Color']=request('Navbar_Txt_Color');
         $data['Navbar_Hover_BG_Color']=request('Navbar_Hover_BG_Color');
         $data['Navbar_Hover_Txt_Color']=request('Navbar_Hover_Txt_Color');
         $data['Navbar_Category_BG_Color']=request('Navbar_Category_BG_Color');
         $data['Navbar_Category_Txt_Color']=request('Navbar_Category_Txt_Color');
         $data['Navbar_Category_Box_BG_Color']=request('Navbar_Category_Box_BG_Color');
         $data['Navbar_Category_Box_Txt_Color']=request('Navbar_Category_Box_Txt_Color');
         $data['Footer_Txt_Hover_Color']=request('Footer_Txt_Hover_Color');
         $data['Footer_Social_BG_Color']=request('Footer_Social_BG_Color');



         MainEComDesign::orderBy('id','desc')->update($data);


             session()->flash('success',trans('admin.Updated'));
             return back();

    }


       public function AddHomeEComDesignFirst(){


         $data['Slider_BG_Color']=null;

         HomeEComDesign::create($data);


             return back();

    }

      public function AddHomeEComDesign(){



          $image=request()->file('Slider_BG_Image');
          if($image){
            $image_name=Str::random(20);
            $ext=strtolower($image->getClientOriginalExtension());
            $image_full_name=$image_name .'.' . $ext ;
            $upload_path='LogoImages/';
            $image_url=$upload_path.$image_full_name;
            $success=$image->move($upload_path,$image_full_name);
                   }


             if(!empty($image_url)){

                 $data['Slider_BG_Image']=$image_url;

             }else{
                 $data['Slider_BG_Image']=request('Images');
             }



         $data['Slider_BG_Type']=request('Slider_BG_Type');

         $data['Slider_BG_Color']=request('Slider_BG_Color');
         $data['Slider_Button_BG_Color']=request('Slider_Button_BG_Color');
         $data['Slider_Button_Txt_Color']=request('Slider_Button_Txt_Color');
         $data['Slider_Button_Hover_BG_Color']=request('Slider_Button_Hover_BG_Color');
         $data['Slider_Button_Hover_Txt_Color']=request('Slider_Button_Hover_Txt_Color');
         $data['Slider_Title_Txt_Color']=request('Slider_Title_Txt_Color');
         $data['Slider_Desc_Txt_Color']=request('Slider_Desc_Txt_Color');
             $data['Ads_Top_Img_First_BG_Color']=request('Ads_Top_Img_First_BG_Color');
         $data['Ads_Top_Img_First_Before_BG_Color']=request('Ads_Top_Img_First_Before_BG_Color');
         $data['Ads_Top_Img_Second_BG_Color']=request('Ads_Top_Img_Second_BG_Color');
         $data['Ads_Top_Img_Second_Before_BG_Color']=request('Ads_Top_Img_Second_Before_BG_Color');
         $data['Ads_Top_Img_Button_BG_Color']=request('Ads_Top_Img_Button_BG_Color');
         $data['Ads_Top_Img_Button_Txt_Color']=request('Ads_Top_Img_Button_Txt_Color');
         $data['Ads_Top_Img_Button_Hover_BG_Color']=request('Ads_Top_Img_Button_Hover_BG_Color');
         $data['Ads_Top_Img_Button_Hover_Txt_Color']=request('Ads_Top_Img_Button_Hover_Txt_Color');
         $data['Support_Icons_BG_Color']=request('Support_Icons_BG_Color');
                 $data['Support_Icons_Txt_Color']=request('Support_Icons_Txt_Color');
         $data['Support_Icons_Color']=request('Support_Icons_Color');
         $data['Ads_Bootom_Imgs_BG_Color']=request('Ads_Bootom_Imgs_BG_Color');
         $data['Ads_Bootom_Imgs_Middle_BG_Color']=request('Ads_Bootom_Imgs_Middle_BG_Color');
         $data['Ads_Bootom_Imgs_Button_BG_Color']=request('Ads_Bootom_Imgs_Button_BG_Color');
         $data['Ads_Bootom_Imgs_Button_Txt_Color']=request('Ads_Bootom_Imgs_Button_Txt_Color');
         $data['Ads_Bootom_Imgs_Button_Hover_BG_Color']=request('Ads_Bootom_Imgs_Button_Hover_BG_Color');
         $data['Ads_Bootom_Imgs_Button_Hover_Txt_Color']=request('Ads_Bootom_Imgs_Button_Hover_Txt_Color');
         $data['Partners_BG_Color']=request('Partners_BG_Color');




         HomeEComDesign::orderBy('id','desc')->update($data);


             session()->flash('success',trans('admin.Updated'));
             return back();

    }


           public function AddHomeProductEComDesignFirst(){


         $data['Special_Offer_Title_BG_Color']=null;

         HomeProductEComDesign::create($data);


             return back();

    }

      public function AddHomeProductEComDesign(){


         $data['Special_Offer_Title_BG_Color']=request('Special_Offer_Title_BG_Color');
         $data['Special_Offer_Title_Txt_Color']=request('Special_Offer_Title_Txt_Color');
         $data['Special_Offer_Product_BG_Color']=request('Special_Offer_Product_BG_Color');
         $data['Special_Offer_Product_Border_Color']=request('Special_Offer_Product_Border_Color');
         $data['Special_Offer_Product_Txt_Color']=request('Special_Offer_Product_Txt_Color');
         $data['Special_Offer_Product_Price_Color']=request('Special_Offer_Product_Price_Color');
         $data['Special_Offer_Product_Rate_Color']=request('Special_Offer_Product_Rate_Color');
          $data['Best_Sellers_Title_BG_Color']=request('Best_Sellers_Title_BG_Color');
         $data['Best_Sellers_Title_Txt_Color']=request('Best_Sellers_Title_Txt_Color');
         $data['Best_Sellers_Category_Txt_Color']=request('Best_Sellers_Category_Txt_Color');
         $data['Best_Sellers_Category_Active_Txt_Color']=request('Best_Sellers_Category_Active_Txt_Color');
         $data['Best_Sellers_Product_BG_Color']=request('Best_Sellers_Product_BG_Color');
         $data['Best_Sellers_Product_Group_BG_Color']=request('Best_Sellers_Product_Group_BG_Color');
         $data['Best_Sellers_Product_Group_Txt_Color']=request('Best_Sellers_Product_Group_Txt_Color');
         $data['Best_Sellers_Product_Group_Hover_BG_Color']=request('Best_Sellers_Product_Group_Hover_BG_Color');
         $data['Best_Sellers_Product_Group_Hover_Txt_Color']=request('Best_Sellers_Product_Group_Hover_Txt_Color');
        $data['Best_Sellers_Product_Icon_BG_Color']=request('Best_Sellers_Product_Icon_BG_Color');
         $data['Best_Sellers_Product_Icon_Txt_Color']=request('Best_Sellers_Product_Icon_Txt_Color');
         $data['Best_Sellers_Product_Icon_Hover_BG_Color']=request('Best_Sellers_Product_Icon_Hover_BG_Color');
         $data['Best_Sellers_Product_Icon_Hover_Txt_Color']=request('Best_Sellers_Product_Icon_Hover_Txt_Color');
         $data['Best_Sellers_Product_Txt_Color']=request('Best_Sellers_Product_Txt_Color');
         $data['Best_Sellers_Product_Price_Color']=request('Best_Sellers_Product_Price_Color');
         $data['Best_Sellers_Product_Rate_Color']=request('Best_Sellers_Product_Rate_Color');
         $data['New_Arrivals_Title_BG_Color']=request('New_Arrivals_Title_BG_Color');
         $data['New_Arrivals_Title_Txt_Color']=request('New_Arrivals_Title_Txt_Color');
         $data['New_Arrivals_Product_BG_Color']=request('New_Arrivals_Product_BG_Color');
         $data['New_Arrivals_Product_Group_BG_Color']=request('New_Arrivals_Product_Group_BG_Color');
         $data['New_Arrivals_Product_Group_Txt_Color']=request('New_Arrivals_Product_Group_Txt_Color');
         $data['New_Arrivals_Product_Group_Hover_BG_Color']=request('New_Arrivals_Product_Group_Hover_BG_Color');
         $data['New_Arrivals_Product_Group_Hover_Txt_Color']=request('New_Arrivals_Product_Group_Hover_Txt_Color');
         $data['New_Arrivals_Product_Icon_BG_Color']=request('New_Arrivals_Product_Icon_BG_Color');
         $data['New_Arrivals_Product_Icon_Txt_Color']=request('New_Arrivals_Product_Icon_Txt_Color');
         $data['New_Arrivals_Product_Icon_Hover_BG_Color']=request('New_Arrivals_Product_Icon_Hover_BG_Color');
         $data['New_Arrivals_Product_Icon_Hover_Txt_Color']=request('New_Arrivals_Product_Icon_Hover_Txt_Color');
         $data['New_Arrivals_Product_Txt_Color']=request('New_Arrivals_Product_Txt_Color');
         $data['New_Arrivals_Product_Price_Color']=request('New_Arrivals_Product_Price_Color');
         $data['New_Arrivals_Product_Hover_Price_Color']=request('New_Arrivals_Product_Hover_Price_Color');
         $data['New_Arrivals_Product_Rate_Color']=request('New_Arrivals_Product_Rate_Color');
         $data['Special_Offer_Product_Txt_Hover_Color']=request('Special_Offer_Product_Txt_Hover_Color');




         HomeProductEComDesign::orderBy('id','desc')->update($data);


             session()->flash('success',trans('admin.Updated'));
             return back();

    }



            public function AddSupPagesEComDesignFirst(){


         $data['About_Title_Color']=null;

         SupPagesEComDesign::create($data);


             return back();

    }

      public function AddSupPagesEComDesign(){


         $data['About_Title_Color']=request('About_Title_Color');
         $data['About_Txt_Color']=request('About_Txt_Color');
         $data['Blogs_Title_Color']=request('Blogs_Title_Color');
         $data['Blogs_Txt_Color']=request('Blogs_Txt_Color');
         $data['Blogs_Hover_Txt_Color']=request('Blogs_Hover_Txt_Color');
         $data['Contact_Title_Color']=request('Contact_Title_Color');
         $data['Contact_Txt_Color']=request('Contact_Txt_Color');
          $data['Contact_Form_Input_Border_Color']=request('Contact_Form_Input_Border_Color');
         $data['Contact_Form_Input_Txt_Color']=request('Contact_Form_Input_Txt_Color');
         $data['Contact_Form_Button_BG_Color']=request('Contact_Form_Button_BG_Color');
         $data['Contact_Form_Button_Txt_Color']=request('Contact_Form_Button_Txt_Color');
         $data['Contact_Form_Button_Hover_BG_Color']=request('Contact_Form_Button_Hover_BG_Color');
         $data['Contact_Form_Button_Hover_Txt_Color']=request('Contact_Form_Button_Hover_Txt_Color');
         $data['Faq_Title_Color']=request('Faq_Title_Color');
         $data['Faq_Q_BG_Color']=request('Faq_Q_BG_Color');
         $data['Faq_Q_Txt_Color']=request('Faq_Q_Txt_Color');
        $data['Faq_A_Line_Color']=request('Faq_A_Line_Color');
         $data['Faq_A_BG_Color']=request('Faq_A_BG_Color');
         $data['Faq_A_Txt_Color']=request('Faq_A_Txt_Color');
         $data['MyAccount_Box_BG_Color']=request('MyAccount_Box_BG_Color');
         $data['MyAccount_Box_Button_BG_Color']=request('MyAccount_Box_Button_BG_Color');
         $data['MyAccount_Box_Button_Txt_Color']=request('MyAccount_Box_Button_Txt_Color');
         $data['MyAccount_Box_Button_Hover_BG_Color']=request('MyAccount_Box_Button_Hover_BG_Color');
         $data['MyAccount_Box_Button_Hover_Txt_Color']=request('MyAccount_Box_Button_Hover_Txt_Color');
         $data['MyAccount_Box_Input_Border_Color']=request('MyAccount_Box_Input_Border_Color');
         $data['MyAccount_Box_Input_Txt_Color']=request('MyAccount_Box_Input_Txt_Color');


         SupPagesEComDesign::orderBy('id','desc')->update($data);


             session()->flash('success',trans('admin.Updated'));
             return back();

    }



           public function AddSupPagesPartTwoEComDesignFirst(){


         $data['Shop_Product_BG_Color']=null;

         SupPagesPartTwoEComDesign::create($data);


             return back();

    }

      public function AddSupPagesPartTwoEComDesign(){


         $data['Shop_Product_BG_Color']=request('Shop_Product_BG_Color');
         $data['Shop_Product_Group_BG_Color']=request('Shop_Product_Group_BG_Color');
         $data['Shop_Product_Group_Txt_Color']=request('Shop_Product_Group_Txt_Color');
         $data['Shop_Product_Group_Hover_BG_Color']=request('Shop_Product_Group_Hover_BG_Color');
         $data['Shop_Product_Group_Hover_Txt_Color']=request('Shop_Product_Group_Hover_Txt_Color');
         $data['Shop_Product_Icon_BG_Color']=request('Shop_Product_Icon_BG_Color');
         $data['Shop_Product_Icon_Txt_Color']=request('Shop_Product_Icon_Txt_Color');
          $data['Shop_Product_Icon_Hover_BG_Color']=request('Shop_Product_Icon_Hover_BG_Color');
         $data['Shop_Product_Icon_Hover_Txt_Color']=request('Shop_Product_Icon_Hover_Txt_Color');
         $data['Shop_Product_Txt_Color']=request('Shop_Product_Txt_Color');
         $data['Shop_Product_Price_Color']=request('Shop_Product_Price_Color');
         $data['Shop_Product_Rate_Color']=request('Shop_Product_Rate_Color');
         $data['Shop_Filter_Title_Color']=request('Shop_Filter_Title_Color');
         $data['Shop_Filter_Txt_Color']=request('Shop_Filter_Txt_Color');
         $data['Shop_Filter_Search_BG_Color']=request('Shop_Filter_Search_BG_Color');
         $data['Shop_Filter_Search_Icon_BG_Color']=request('Shop_Filter_Search_Icon_BG_Color');
        $data['Shop_Filter_Search_Icon_Txt_Color']=request('Shop_Filter_Search_Icon_Txt_Color');
         $data['Shop_Filter_Search_Icon_BG_Hover_Color']=request('Shop_Filter_Search_Icon_BG_Hover_Color');
         $data['Shop_Filter_Search_Icon_Txt_Hover_Color']=request('Shop_Filter_Search_Icon_Txt_Hover_Color');
         $data['Cart_Button_BG_Color']=request('Cart_Button_BG_Color');
         $data['Cart_Button_Txt_Color']=request('Cart_Button_Txt_Color');
         $data['Cart_Button_BG_Hover_Color']=request('Cart_Button_BG_Hover_Color');
         $data['Cart_Button_Txt_Hover_Color']=request('Cart_Button_Txt_Hover_Color');
         $data['Cart_Box_BG_Color']=request('Cart_Box_BG_Color');
         $data['Cart_Box_Title_Color']=request('Cart_Box_Title_Color');
 $data['Cart_Box_Txt_Color']=request('Cart_Box_Txt_Color');
         $data['Cart_Box_Button_BG_Color']=request('Cart_Box_Button_BG_Color');
         $data['Cart_Box_Button_Txt_Color']=request('Cart_Box_Button_Txt_Color');
         $data['Cart_Box_Button_BG_Hover_Color']=request('Cart_Box_Button_BG_Hover_Color');
         $data['Cart_Box_Button_Txt_Hover_Color']=request('Cart_Box_Button_Txt_Hover_Color');
         $data['Cart_Cupon_Button_BG_Color']=request('Cart_Cupon_Button_BG_Color');
         $data['Cart_Cupon_Button_Txt_Color']=request('Cart_Cupon_Button_Txt_Color');
          $data['Cart_Cupon_Button_BG_Hover_Color']=request('Cart_Cupon_Button_BG_Hover_Color');
         $data['Cart_Cupon_Button_Txt_Hover_Color']=request('Cart_Cupon_Button_Txt_Hover_Color');
         $data['Cart_Cupon_Input_BG_Color']=request('Cart_Cupon_Input_BG_Color');
         $data['Checkout_Box_BG_Color']=request('Checkout_Box_BG_Color');
         $data['Checkout_Box_Title_Color']=request('Checkout_Box_Title_Color');
         $data['Checkout_Box_Txt_Color']=request('Checkout_Box_Txt_Color');
         $data['Checkout_Box_Button_BG_Color']=request('Checkout_Box_Button_BG_Color');
         $data['Checkout_Box_Button_Txt_Color']=request('Checkout_Box_Button_Txt_Color');
         $data['Checkout_Box_Button_BG_Hover_Color']=request('Checkout_Box_Button_BG_Hover_Color');
        $data['Checkout_Box_Button_Txt_Hover_Color']=request('Checkout_Box_Button_Txt_Hover_Color');
         $data['Checkout_Head_BG_Color']=request('Checkout_Head_BG_Color');
         $data['Checkout_Head_Txt_Color']=request('Checkout_Head_Txt_Color');
         $data['Checkout_Input_BG_Color']=request('Checkout_Input_BG_Color');
         $data['Checkout_Input_Txt_Color']=request('Checkout_Input_Txt_Color');
         $data['Reg_Login_Form_Box_BG_Color']=request('Reg_Login_Form_Box_BG_Color');
         $data['Reg_Login_Form_Box_Title_Color']=request('Reg_Login_Form_Box_Title_Color');
         $data['Reg_Login_Form_Box_Txt_Color']=request('Reg_Login_Form_Box_Txt_Color');
         $data['Reg_Login_Form_Box_Txt_Hover_Color']=request('Reg_Login_Form_Box_Txt_Hover_Color');
         $data['Reg_Login_Form_Box_Input_Border_Color']=request('Reg_Login_Form_Box_Input_Border_Color');
         $data['Reg_Login_Form_Box_Button_BG_Color']=request('Reg_Login_Form_Box_Button_BG_Color');
         $data['Reg_Login_Form_Box_Button_Txt_Color']=request('Reg_Login_Form_Box_Button_Txt_Color');
         $data['Reg_Login_Form_Box_Button_BG_Hover_Color']=request('Reg_Login_Form_Box_Button_BG_Hover_Color');
         $data['Reg_Login_Form_Box_Button_Txt_Hover_Color']=request('Reg_Login_Form_Box_Button_Txt_Hover_Color');



         SupPagesPartTwoEComDesign::orderBy('id','desc')->update($data);


             session()->flash('success',trans('admin.Updated'));
             return back();

    }




          public function AddProductDetailsEComDesignFirst(){


         $data['Title_Color']=null;

         ProductDetailsEComDesign::create($data);


             return back();

    }

      public function AddProductDetailsEComDesign(){


         $data['Title_Color']=request('Title_Color');
         $data['Txt_Color']=request('Txt_Color');
         $data['Price_Color']=request('Price_Color');
         $data['Rate_Color']=request('Rate_Color');
         $data['Select_BG_Color']=request('Select_BG_Color');
         $data['Select_Txt_Color']=request('Select_Txt_Color');
         $data['Qty_BG_Color']=request('Qty_BG_Color');
          $data['Qty_Txt_Color']=request('Qty_Txt_Color');
         $data['Qty_Input_BG_Color']=request('Qty_Input_BG_Color');
         $data['Qty_Input_Txt_Color']=request('Qty_Input_Txt_Color');
         $data['Comment_Input_BG_Color']=request('Comment_Input_BG_Color');
         $data['Comment_Button_BG_Color']=request('Comment_Button_BG_Color');
         $data['Comment_Button_Txt_Color']=request('Comment_Button_Txt_Color');
         $data['Comment_Button_BG_Hover_Color']=request('Comment_Button_BG_Hover_Color');
         $data['Comment_Button_Txt_Hover_Color']=request('Comment_Button_Txt_Hover_Color');
         $data['Related_Title_BG_Color']=request('Related_Title_BG_Color');
        $data['Related_Title_Txt_Color']=request('Related_Title_Txt_Color');
         $data['Related_Product_BG_Color']=request('Related_Product_BG_Color');
         $data['Related_Product_Group_BG_Color']=request('Related_Product_Group_BG_Color');
         $data['Related_Product_Group_Txt_Color']=request('Related_Product_Group_Txt_Color');
         $data['Related_Product_Group_Hover_BG_Color']=request('Related_Product_Group_Hover_BG_Color');
         $data['Related_Product_Group_Hover_Txt_Color']=request('Related_Product_Group_Hover_Txt_Color');
         $data['Related_Product_Icon_BG_Color']=request('Related_Product_Icon_BG_Color');
         $data['Related_Product_Icon_Txt_Color']=request('Related_Product_Icon_Txt_Color');
         $data['Related_Product_Icon_Hover_BG_Color']=request('Related_Product_Icon_Hover_BG_Color');
         $data['Related_Product_Icon_Hover_Txt_Color']=request('Related_Product_Icon_Hover_Txt_Color');
         $data['Related_Product_Txt_Color']=request('Related_Product_Txt_Color');
         $data['Related_Product_Price_Color']=request('Related_Product_Price_Color');
         $data['Related_Product_Hover_Price_Color']=request('Related_Product_Hover_Price_Color');
         $data['Related_Product_Rate_Color']=request('Related_Product_Rate_Color');


         ProductDetailsEComDesign::orderBy('id','desc')->update($data);


             session()->flash('success',trans('admin.Updated'));
             return back();

    }




              public function AddPagesWishCompEComDesignFirst(){


         $data['Wish_Title_BG_Color']=null;

         SupPagesWishCompEComDesign::create($data);


             return back();

    }

      public function AddSupPagesWishCompEComDesign(){


         $data['Wish_Title_BG_Color']=request('Wish_Title_BG_Color');
         $data['Wish_Title_Txt_Color']=request('Wish_Title_Txt_Color');
         $data['Wish_Box_BG_Color']=request('Wish_Box_BG_Color');
         $data['Wish_Box_Border_Color']=request('Wish_Box_Border_Color');
         $data['Wish_Box_Border_Type']=request('Wish_Box_Border_Type');
         $data['Wish_Box_Txt_Color']=request('Wish_Box_Txt_Color');
         $data['Wish_Box_Button_BG_Color']=request('Wish_Box_Button_BG_Color');
          $data['Wish_Box_Button_Txt_Color']=request('Wish_Box_Button_Txt_Color');
         $data['Wish_Box_Button_BG_Hover_Color']=request('Wish_Box_Button_BG_Hover_Color');
         $data['Wish_Box_Button_Txt_Hover_Color']=request('Wish_Box_Button_Txt_Hover_Color');
         $data['Compare_Box_BG_Color']=request('Compare_Box_BG_Color');
         $data['Compare_Box_Txt_Color']=request('Compare_Box_Txt_Color');
         $data['Compare_Box_Price_Hover_Color']=request('Compare_Box_Price_Hover_Color');
         $data['Compare_Box_Delete_Txt_Color']=request('Compare_Box_Delete_Txt_Color');





         SupPagesWishCompEComDesign::orderBy('id','desc')->update($data);


             session()->flash('success',trans('admin.Updated'));
             return back();

    }


//========================== View ==============================

    //Session

     public function OldChangeCountrySession($id){
           Cart::destroy();
             session()->put('ChangeCountryy',$id);
                 // return redirect('/');
                  return back();
    }


 public function ChangeCountrySession(){

     $id=request('Country');
           Cart::destroy();
             session()->put('ChangeCountryy',$id);
                 // return redirect('/');
                  return back();
    }


    //Home Page
      public function HomePage(){

        $Def=CompanyData::orderBy('id','desc')->first();
        $Footer=Footer::orderBy('id','desc')->first();
             $Social=SocialMedia::orderBy('id','desc')->first();
        $HowWeWork=HowWeWork::orderBy('id','desc')->first();
        $HowWeWork1=HowWorkIcons::find(1);
        $HowWeWork2=HowWorkIcons::find(2);
        $HowWeWork3=HowWorkIcons::find(3);
        $HowWeWork4=HowWorkIcons::find(4);
        $About=About::orderBy('id','desc')->first();
        $Services=Services::all();
        $Articles=Articles::take(3)->get();
        $Testiminoals=Testiminoals::all();
        $Webslider=Webslider::where(function($query) {
            $query->where('Type', 0)->orWhereNull('Type');
        })->get(); // Get website sliders (Type = 0 or NULL for legacy data)
        $Features=Features::all();
        $Gallery=Gallery::all();
        $Products=Products::all();

        return view('site.home',[
        'About'=>$About,
        'Services'=>$Services,
        'Def'=>$Def,
        'Comp'=>$Def, // Add Comp as alias for Def for backward compatibility
        'HowWeWork'=>$HowWeWork,
        'Articles'=>$Articles,
        'Testiminoals'=>$Testiminoals,
        'Webslider'=>$Webslider,
        'Sliders'=>$Webslider, // Add Sliders as alias for Webslider for backward compatibility
        'HowWeWork1'=>$HowWeWork1,
        'HowWeWork2'=>$HowWeWork2,
        'HowWeWork3'=>$HowWeWork3,
        'HowWeWork4'=>$HowWeWork4,
        'Footer'=>$Footer,
        'Features'=>$Features,
        'Gallery'=>$Gallery,
        'Products'=>$Products,
        'Social'=>$Social,

        ]);
    }

    //Prescription Product Page
    public function PrescriptionPro($id){
        $product = Products::find($id);
        if(!$product) {
            return redirect('/');
        }

        $Def=CompanyData::orderBy('id','desc')->first();
        $Footer=Footer::orderBy('id','desc')->first();
        $Social=SocialMedia::orderBy('id','desc')->first();

        return view('site.prescription',[
            'Pro'=>$product,
            'Def'=>$Def,
            'Footer'=>$Footer,
            'Social'=>$Social,
        ]);
    }

    //Blog Details Page
    public function BlogsDet($id){
        $article = Articles::find($id);
        if(!$article) {
            return redirect('/');
        }

        $Def=CompanyData::orderBy('id','desc')->first();
        $Footer=Footer::orderBy('id','desc')->first();
        $Social=SocialMedia::orderBy('id','desc')->first();

        return view('site.blog-details',[
            'article'=>$article,
            'Def'=>$Def,
            'Footer'=>$Footer,
            'Social'=>$Social,
        ]);
    }

    //Blog Site Page
    public function BlogSite(){
        $Def=CompanyData::orderBy('id','desc')->first();
        $Footer=Footer::orderBy('id','desc')->first();
        $Social=SocialMedia::orderBy('id','desc')->first();
        $Articles=Articles::orderBy('id','desc')->paginate(9);

        return view('site.blog',[
            'Articles'=>$Articles,
            'Def'=>$Def,
            'Footer'=>$Footer,
            'Social'=>$Social,
        ]);
    }

    //Policy Site Page
    public function PolicySite(){
        $Def=CompanyData::orderBy('id','desc')->first();
        $Footer=Footer::orderBy('id','desc')->first();
        $Social=SocialMedia::orderBy('id','desc')->first();
        $Privacy=Privacy::orderBy('id','desc')->first();

        return view('site.policy',[
            'Privacy'=>$Privacy,
            'item'=>$Privacy, // Add item as alias for Privacy for backward compatibility
            'Def'=>$Def,
            'Footer'=>$Footer,
            'Social'=>$Social,
        ]);
    }

     public function AboutSite(){

          $About=About::orderBy('id','desc')->first();
         return view('site.About',[
            'About'=>$About,

         ]);
    }

       public function ContactSite(){

          $Contact=ContactUS::orderBy('id','desc')->first();
         return view('site.Contact',[
            'Contact'=>$Contact,

         ]);
    }

       public function PrivacyPolicySite(){

          $Ploicy=Polices::orderBy('id','desc')->first();
         return view('site.Polices',[
            'Ploicy'=>$Ploicy,

         ]);
    }

       public function TermsSite(){

          $Terms=Terms::orderBy('id','desc')->first();
         return view('site.Terms',[
            'Terms'=>$Terms,

         ]);
    }

       public function FAQSite(){

          $FAQs=FAQ::all();
         return view('site.FAQs',[
            'FAQs'=>$FAQs,

         ]);
    }

       public function BlogsSite(){

          $Articles=Articles::paginate(3);
         return view('site.Articles',[
            'Articles'=>$Articles,

         ]);
    }

     public function BlogDetails($id){

          $art=Articles::find($id);
         $Articles=Articles::paginate(3);
         return view('site.ArticleDetails',[
            'art'=>$art,
            'Articles'=>$Articles,

         ]);
    }


      //PostMsgRqst
      public function PostMsgRqst(){

        $data= $this->validate(request(),[
             'Name'=>'required',
             'Email'=>'required',
             'Subject'=>'required',
             'Msg'=>'required',
             'Phone'=>'required',
               ],[


         ]);

         $data['Name']=request('Name');
         $data['Email']=request('Email');
         $data['Subject']=request('Subject');
         $data['Msg']=request('Msg');
         $data['Phone']=request('Phone');
         MsgRqst::create($data);

             session()->flash('success',trans('admin.Thanks'));
             return back();

    }

    //Login Site
     public function LoginSite(){
         return view('site.Login');
    }

       public function RegisterSite(){
            $Countris=Countris::all();
         return view('site.Register',['Countris'=>$Countris]);
    }

    public function PostRegister(){

        $data= $this->validate(request(),[
             'Name'=>'required',
             'email'=>'required|email|unique:customers',
             'Phone'=>'required|unique:customers',
             'password'=>'required|min:6',

               ],[
            'Name.required' => trans('admin.nameRequired'),
            'email.required' => trans('admin.emailRequired'),
            'email.email' =>trans('admin.emailEmail'),
            'email.unique' =>trans('admin.emailUnique'),
            'password.required' =>trans('admin.passwordRequired'),
            'password.min' => trans('admin.passwordmin_6'),


         ]);


             $count=AcccountingManual::orderBy('id','desc')->where('Parent',24)->count();
            $code=AcccountingManual::orderBy('id','desc')->where('Parent',24)->first();
            $codee=AcccountingManual::find(24);

                if($count == 0){

                $x=$codee->Code.'01';
             $dataX['Code']=(int) $x ;

                }else{

                                $y=substr($code->Code, strlen($codee->Code));
        $newY=$y + 1 ;

            if(strlen($newY) == 1){
                $NewXY='0'.$newY;
            }else{
              $NewXY=$newY;
            }
                $x= $codee->Code.$NewXY;
                  $dataX['Code']=(int) $x;

                }

         $dataX['Name']=request('Name');
         $dataX['NameEn']=request('Name');
         $dataX['Type']=1;
         $dataX['Parent']=24;
         $dataX['Note']=null;
         $dataX['User']=null;
         AcccountingManual::create($dataX);

         $Acc=AcccountingManual::orderBy('id','desc')->first();

             $res=Customers::orderBy('id','desc')->first();

           if(!empty($res->Code)){

              $CCODE=$res->Code + 1 ;

           }else{

              $CCODE=1;

           }

   $data['Code']=$CCODE;
        $data['Date']=date('Y-m-d');
        $data['Name']=request('Name');
        $data['NameEn']=request('Name');
        $data['Price_Level']=1;
        $data['Phone']=request('Phone');
        $data['email']=request('email');
        $data['password']=bcrypt(request('password'));
        $data['country']=request('country');
    $data['Account']=$Acc->id;
     Customers::create($data);

             session()->flash('success',trans('admin.Registerd_Successfully'));
             return back();

    }

       public function PostLoginSite(){


       if(auth()->guard('client')->attempt(['email'=>request('Email'),'password'=>request('Password')])){

          return redirect('/');

       }else{
          session()->flash('error',trans('admin.incorrect_information_login'));
       	 return back();
       }



    }

         public function LogoutSite(){
            Cart::destroy();
    auth()->guard('client')->logout();
	     return redirect('/');

 }

     public function ForgotSite(){
         return view('site.Forget');
    }

     public function PostForgotSite(){

         $admin = Customers::where('email',request('Email'))->first();
               if(!empty($admin)){


                   $code=rand(10,10000);
                   Customers::where('id',$admin->id)->update(['code'=>$code]);

                 Mail::to($admin->Email)->send(new UserResetPassword(['data'=>$admin]));
                   session()->flash('success',trans('admin.Reset_Password'));

                    return view('site.VerifyCode',['admin'=>$admin]);

               }else{

                session()->flash('success',trans('admin.WrongEmail'));
  return back();

               }



        }

  public function PostCodeSite(){


         $admin = Customers::where('id',request('user'))->first();
               if($admin->code == request('code')){

                   Customers::where('id',$admin->id)->update(['code'=>null]);

                    return view('site.ResetPassword',['admin'=>$admin]);

               }else{

                session()->flash('error',trans('admin.WrongCode'));
                return view('site.VerifyCode',['admin'=>$admin]);

               }



        }

     public function PostResetPassword(){

        $data= $this->validate(request(),[

             'Password'=>'required|min:6|same:confirm',

               ],[

            'Password.required' =>trans('admin.passwordRequired'),
            'Password.min' => trans('admin.passwordmin_6'),
            'Password.same' => trans('admin.passwordsame'),

         ]);


         $data['password']=bcrypt(request('Password'));

         Customers::where('id',request('user'))->update($data);

          if(auth()->guard('client')->attempt(['email'=>request('Email'),'password'=>request('Password')])){

          return redirect('/');

       }


    }


//My Account
  public function MyAccountSite(){
         $User=Customers::find(auth()->guard('client')->user()->id);
      $Addresses=Addressses::where('Customer',auth()->guard('client')->user()->id)->get();
      $Countris=Countris::all();
      $Governrates=Governrate::where('Country',auth()->guard('client')->user()->country)->get();
      $Orders=SalesOrder::where('Client',auth()->guard('client')->user()->Account)->paginate(9);
         return view('site.MyAccount',['User'=>$User,'Addresses'=>$Addresses,'Countris'=>$Countris,'Governrates'=>$Governrates,'Orders'=>$Orders]);
    }

    //My Profile
    public function MyProfileSite(){
        $Def=CompanyData::orderBy('id','desc')->first();
        $Footer=Footer::orderBy('id','desc')->first();
        $Social=SocialMedia::orderBy('id','desc')->first();
        $User=Customers::find(auth()->guard('client')->user()->id);
        $Addresses=Addressses::where('Customer',auth()->guard('client')->user()->id)->get();
        $Countris=Countris::all();
        $Governrates=Governrate::where('Country',auth()->guard('client')->user()->country)->get();
        $Orders=SalesOrder::where('Client',auth()->guard('client')->user()->Account)->paginate(9);

        return view('site.MyProfile',[
            'User'=>$User,
            'Addresses'=>$Addresses,
            'Countris'=>$Countris,
            'Governrates'=>$Governrates,
            'Orders'=>$Orders,
            'Def'=>$Def,
            'Footer'=>$Footer,
            'Social'=>$Social,
        ]);
    }

     public function UpdateAccount(){

        $data= $this->validate(request(),[
             'Name'=>'required',
              'email'=>'required|email|unique:customers,email,'.auth()->guard('client')->user()->id,
             'Phone'=>'required|unique:customers,Phone,'.auth()->guard('client')->user()->id,

               ],[
            'Name.required' => trans('admin.nameRequired'),
            'country.required' => trans('admin.countryRequired'),
            'email.required' => trans('admin.emailRequired'),
            'email.email' =>trans('admin.emailEmail'),
            'email.unique' =>trans('admin.emailUnique'),

         ]);


         $data['Name']=request('Name');
         $data['NameEn']=request('Name');
       $data['Phone']=request('Phone');
    $data['email']=request('email');
    $data['country']=request('country');

         Customers::where('id',auth()->guard('client')->user()->id)->update($data);

             session()->flash('success',trans('admin.Updated'));
             return back();

    }


        public function GovernrateFilter($id) {

               if(app()->getLocale() == 'ar' ){
       $states = DB::table("cities")->where("Gov",$id)->pluck("Arabic_Name","id");
               }else{
          $states = DB::table("cities")->where("Gov",$id)->pluck("English_Name","id");
               }

       return response()->json($states);

    }

      public function CityFilter($id) {

               if(app()->getLocale() == 'ar' ){
       $states = DB::table("places")->where("City",$id)->pluck("Arabic_Name","id");
               }else{
           $states = DB::table("places")->where("City",$id)->pluck("English_Name","id");
               }

       return response()->json($states);

    }

    public function CityShip($id) {


        $states=[];

       $X = DB::table("cities")->where("id",$id)->first();


           $states +=['ship'=>$X->Ship_Price];

       return response()->json($states);

    }


     public function UpdatePassword(){

        $data= $this->validate(request(),[

             'password'=>'required|min:6|same:Confirm_Password',

               ],[

            'password.required' =>trans('admin.passwordRequired'),
            'password.min' => trans('admin.passwordmin_6'),
            'password.same' => trans('admin.passwordsame'),

         ]);

         $user= Customers::find(auth()->guard('client')->user()->id);



             if (Hash::check(request('CurrentPassword'), $user->password)) {

                       $data['password']=bcrypt(request('password'));

                Customers::where('id',auth()->guard('client')->user()->id)->update($data);
                       session()->flash('success',trans('admin.Updated'));
             return back();

             }else{

                         session()->flash('error',trans('admin.CurrentPassWrong'));
             return back();
             }






    }


     public function EditMyAddress(){



            $Addr['Address_Name']=request('Address_Name');
            $Addr['Street']=request('StreetAdd');
            $Addr['Special_Mark']=request('Special_MarkAdd');
            $Addr['Buliding']=request('BulidingAdd');
            $Addr['Floor']=request('FloorAdd');
            $Addr['Flat']=request('FlatAdd');
            $Addr['Details']=request('Address_DetailsAdd');
            $Addr['Location']=request('LocationAdd');
            $Addr['Governrate']=request('GovernrateAdd');
            $Addr['City']=request('CityAdd');
            $Addr['Place']=request('PlaceAdd');


           Addressses::where('id',request('ID'))->update($Addr);

             session()->flash('success',trans('admin.Updated'));
             return back();

    }
     public function UpdateAddress(){



            Addressses::where('Customer',auth()->guard('client')->user()->id)->delete();

            $Addr['Address_Name']=request('Address_Name');
            $Addr['Street']=request('StreetAdd');
            $Addr['Special_Mark']=request('Special_MarkAdd');
            $Addr['Buliding']=request('BulidingAdd');
            $Addr['Floor']=request('FloorAdd');
            $Addr['Flat']=request('FlatAdd');
            $Addr['Details']=request('Address_DetailsAdd');
            $Addr['Location']=request('LocationAdd');
            $Addr['Governrate']=request('GovernrateAdd');
            $Addr['City']=request('CityAdd');
            $Addr['Place']=request('PlaceAdd');
            $Addr['Customer']=auth()->guard('client')->user()->id;

           Addressses::create($Addr);

             session()->flash('success',trans('admin.Updated'));
             return back();

    }


    public function DeleteMyAddress($id){

            $del=Addressses::find($id);
            $del->delete();
            session()->flash('error',trans('admin.Deleted'));
            return back();

}



//Shop
    public function ShopSite(){
        $Def=CompanyData::orderBy('id','desc')->first();
        $Footer=Footer::orderBy('id','desc')->first();
        $Social=SocialMedia::orderBy('id','desc')->first();

        $Products=Products::whereIn('Store_Show',[1,3])
            ->whereIn('P_Type',['Completed','Single_Variable','Duble_Variable'])
            ->paginate(12);
        $Groups=ItemsGroups::whereIn('Store_Show',[1,3])->where('Parent',0)->get();

        return view('site.Shop',[
            'Products'=>$Products,
            'Groups'=>$Groups,
            'Def'=>$Def,
            'Footer'=>$Footer,
            'Social'=>$Social,
        ]);
    }

    public function ProductDetails($id){
        $pro=Products::find($id);

        // Check if product exists
        if(!$pro) {
            return redirect('/')->with('error', 'Product not found');
        }

        $ProductGroups=Products::orderBy('id','desc')
            ->where('id','!=',$pro->id)
            ->whereIn('Store_Show',[1,3])
            ->where('Group',$pro->Group)
            ->whereIn('P_Type',['Completed','Single_Variable','Duble_Variable'])
            ->take(5)->get();

               $ProductBrands=Products::orderBy('id','desc')
            ->where('id','!=',$pro->id)
            ->whereIn('Store_Show',[1,3])
            ->where('Brand',$pro->Brand)
            ->whereIn('P_Type',['Completed','Single_Variable','Duble_Variable'])
            ->take(5)->get();

        $SubImages=SubImages::where('Product',$pro->id)->get();
        $Comments=Comments::where('Product',$pro->id)->get();
        $countReviews=Comments::where('Product',$pro->id)->count();

        $Uni=ProductUnits::where('Product',$pro->id)->where('Def',1)->first();
        $ProDetailsImg=ProDetailsImg::all();
      $Terms=Terms::orderBy('id','desc')->first();
   $Polices=Polices::orderBy('id','desc')->first();
        $NAMEVirable='';
        $NAMEVirable2='';
$Viras='';
$Viras2='';

        if($pro->P_Type == 'Single_Variable'){

             $v=ProductsVira::where('Product',$pro->id)->orderBy('id','desc')->first();

               if(app()->getLocale() == 'ar' ){
         $NAMEVirable=$v->V1()->first()->V_ID()->first()->Name;
               }else{
               $NAMEVirable=$v->V1()->first()->V_ID()->first()->NameEn;
               }

         $Viras=SubVirables::where('V_ID',$v->V1()->first()->V_ID)->get();

        }elseif($pro->P_Type == 'Duble_Variable'){


              $v=ProductsVira::where('Product',$pro->id)->orderBy('id','desc')->first();



                      if(app()->getLocale() == 'ar' ){
        $NAMEVirable=$v->V1()->first()->V_ID()->first()->Name;
               }else{
          $NAMEVirable=$v->V1()->first()->V_ID()->first()->NameEn;
               }

         $Viras=SubVirables::where('V_ID',$v->V1()->first()->V_ID)->get();




                              if(app()->getLocale() == 'ar' ){
        $NAMEVirable2=$v->V2()->first()->V_ID()->first()->Name;
               }else{
       $NAMEVirable2=$v->V2()->first()->V_ID()->first()->NameEn;
               }

         $Viras2=SubVirables::where('V_ID',$v->V2()->first()->V_ID)->get();


        }


         return view('site.ProductDetails',[
             'pro'=>$pro,
             'Pro'=>$pro, // Add uppercase version for blade compatibility
             'ProductGroups'=>$ProductGroups,
             'ProductBrands'=>$ProductBrands,
             'Products'=>$ProductGroups, // Add Products variable for related products section
             'SubImages'=>$SubImages,
             'Subs'=>$SubImages, // Add Subs variable for blade compatibility
             'Uni'=>$Uni,
             'ProDetailsImg'=>$ProDetailsImg,
             'Terms'=>$Terms,
             'Polices'=>$Polices,
             'Comments'=>$Comments,
             'countReviews'=>$countReviews,
             'NAMEVirable'=>$NAMEVirable,
             'Viras'=>$Viras,
             'NAMEVirable2'=>$NAMEVirable2,
             'Viras2'=>$Viras2,
         ]);
    }


    public function  SiteProQty(Request $request) {


 $CODE = $request->get('CODE');
 $STORE = $request->get('STORE');
 $PRODUCT = $request->get('PRODUCT');
     $states=[];

   $QTY=ProductsQty::
                where('Product',$PRODUCT)
                    ->where('Store',$STORE)
                    ->where('P_Code',$CODE)
                    ->first();


        if(empty($QTY)){

              $QTY=ProductsQty::
                where('Product',$PRODUCT)
                    ->where('Store',$STORE)
                    ->where('PP_Code',$CODE)
                    ->first();

            if(empty($QTY)){
                 $QTY=ProductsQty::
                where('Product',$PRODUCT)
                    ->where('Store',$STORE)
                    ->where('PPP_Code',$CODE)
                    ->first();

                  if(empty($QTY)){
                         $QTY=ProductsQty::
                where('Product',$PRODUCT)
                    ->where('Store',$STORE)
                    ->where('PPPP_Code',$CODE)
                    ->first();

                  }


            }


        }


        if(!empty($QTY)){

          $AvQty=$QTY->Qty;
                   $Code=$QTY->P_Code;

        }else{
          $AvQty=0;
       $Code='';


        }


 $states += ['qty' => $AvQty ,'code'=>$Code];
       return response()->json($states);


    }


      public function  SiteProQtyV(Request $request) {


 $V = $request->get('V');
 $STORE = $request->get('STORE');
 $PRODUCT = $request->get('PRODUCT');
     $states=[];

   $QTY=ProductsQty::
                where('Product',$PRODUCT)
                    ->where('Store',$STORE)
                    ->where('V1',$V)
                    ->first();

        if(!empty($QTY)){

          $AvQty=$QTY->Qty;
          $Code=$QTY->P_Code;
          $price=$QTY->Price_Sale;
        }else{
          $AvQty=0;
            $Code='';
            $price=0;
        }


 $states += ['qty' => $AvQty,'code'=>$Code , 'price'=>$price ];
       return response()->json($states);


    }

      public function  SiteProQtyVV(Request $request) {


 $V = $request->get('V');
 $VV = $request->get('VV');
 $STORE = $request->get('STORE');
 $PRODUCT = $request->get('PRODUCT');
     $states=[];

   $QTY=ProductsQty::
                where('Product',$PRODUCT)
                    ->where('Store',$STORE)
                    ->where('V1',$V)
                    ->where('V2',$VV)
                    ->first();




        if(!empty($QTY)){

          $AvQty=$QTY->Qty;
          $Code=$QTY->P_Code;
              $price=$QTY->Price_Sale;
        }else{
          $AvQty=0;
            $Code='';
                $price=0;
        }


 $states += ['qty' => $AvQty,'code'=>$Code, 'price'=>$price];
       return response()->json($states);


    }

//Filters
     public function ShopFilterName(){
        $Def=CompanyData::orderBy('id','desc')->first();
        $Footer=Footer::orderBy('id','desc')->first();
        $Social=SocialMedia::orderBy('id','desc')->first();

        // Currency/Country session handling (same as original)
        if(empty(session()->get('ChangeCountryy'))) {
            $Coin=Coins::where('Draw',1)->first();
            $Country=Countris::where('Coin',$Coin->id)->first();
            session()->put('ChangeCountryy',$Country->id);
        }
        $Ses=Countris::find(session()->get('ChangeCountryy'));

        // Start with base query
        $query = Products::whereIn('Store_Show',[1,3])
            ->whereIn('P_Type',['Completed','Single_Variable','Duble_Variable']);

        // Search filter
        if(!empty(request('search'))){
            $search = request('search');
            $query->where(function($q) use ($search) {
                $q->where('P_Ar_Name','LIKE','%' . $search . '%')
                  ->orWhere('P_En_Name','LIKE','%' . $search . '%')
                  ->orWhere('Uni_Code','LIKE','%' . $search . '%')
                  ->orWhere('SearchCode1','LIKE','%' . $search . '%')
                  ->orWhere('SearchCode2','LIKE','%' . $search . '%');
            });
        }

        // Price filter - handle null/empty prices properly
        if(!empty(request('min_price')) && request('min_price') > 0){
            $query->where(function($q) {
                $q->where('OfferPrice', '>=', request('min_price'))
                  ->orWhereNull('OfferPrice');  // Include products with no price set
            });
        }

        if(!empty(request('max_price')) && request('max_price') > 0){
            $query->where(function($q) {
                $q->where('OfferPrice', '<=', request('max_price'))
                  ->orWhereNull('OfferPrice')   // Include products with no price set
                  ->orWhere('OfferPrice', 0);   // Include products with 0 price
            });
        }

        // Sorting
        $sort = request('sort');
        switch($sort) {
            case 'name_asc':
                $query->orderBy(app()->getLocale() == 'ar' ? 'P_Ar_Name' : 'P_En_Name', 'asc');
                break;
            case 'name_desc':
                $query->orderBy(app()->getLocale() == 'ar' ? 'P_Ar_Name' : 'P_En_Name', 'desc');
                break;
            case 'price_asc':
                $query->orderBy('OfferPrice', 'asc');
                break;
            case 'price_desc':
                $query->orderBy('OfferPrice', 'desc');
                break;
            case 'newest':
                $query->orderBy('id', 'desc');
                break;
            default:
                $query->orderBy('id', 'desc');
                break;
        }

        $Products = $query->paginate(12)->appends(request()->query());

        // Add currency symbol to products from session
        foreach($Products as $product) {
            $product->Symbol = $Ses->Coin()->first()->Symbol ?? '';

            // Map OfferPrice to Price for view compatibility
            if(!empty($product->OfferPrice)) {
                $product->Price = $product->OfferPrice;
            }

            // Set Offer_Price for special offers
            if(!empty($product->Offer) && $product->Offer == 1) {
                $product->Offer_Price = $product->OfferPrice;
            }
        }

        $Groups = ItemsGroups::whereIn('Store_Show',[1,3])->where('Parent',0)->get();
        $Brands = Brands::whereIn('Store_Show',[1,3])->get();
        $ProductsNew = Products::whereIn('Store_Show',[1,3])
            ->whereIn('P_Type',['Completed','Single_Variable','Duble_Variable'])
            ->where('Store_Type',0)
            ->orderBy('id','desc')->take(5)->get();

        return view('site.Shop',[
            'Products'=>$Products,
            'Groups'=>$Groups,
            'Brands'=>$Brands,
            'ProductsNew'=>$ProductsNew,
            'Def'=>$Def,
            'Footer'=>$Footer,
            'Social'=>$Social,
        ]);
    }

    public function ShopFilterBrand($id){

         $Products=Products::whereIn('Store_Show',[1,3])
              ->where('Brand',$id)
           ->whereIn('P_Type',['Completed','Single_Variable','Duble_Variable'])
            ->paginate(24);

         $Groups=ItemsGroups::whereIn('Store_Show',[1,3])->where('Parent',0)->take(6)->get();
         $Brands=Brands::whereIn('Store_Show',[1,3])->take(6)->get();

                         $ProductsNew=Products::whereIn('Store_Show',[1,3])
      ->whereIn('P_Type',['Completed','Single_Variable','Duble_Variable'])
            ->where('Store_Type',0)
    ->orderBy('id','desc')->take(5)->get();
         return view('site.Shop',[
             'Products'=>$Products,
             'Groups'=>$Groups,
             'Brands'=>$Brands,
             'ProductsNew'=>$ProductsNew,

         ]);
    }

    public function FilterShopCat($id){
        $Products=Products::whereIn('Store_Show',[1,3])
              ->where('Group',$id)
           ->whereIn('P_Type',['Completed','Single_Variable','Duble_Variable'])
            ->paginate(24);

         $Groups=ItemsGroups::whereIn('Store_Show',[1,3])->where('Parent',0)->take(6)->get();
         $Brands=Brands::whereIn('Store_Show',[1,3])->take(6)->get();

                           $ProductsNew=Products::whereIn('Store_Show',[1,3])
      ->whereIn('P_Type',['Completed','Single_Variable','Duble_Variable'])
            ->where('Store_Type',0)
    ->orderBy('id','desc')->take(5)->get();
         return view('site.Shop',[
             'Products'=>$Products,
             'Groups'=>$Groups,
             'Brands'=>$Brands,
             'ProductsNew'=>$ProductsNew,

         ]);
    }


    //Comments
      public function AddComment(){

        $data= $this->validate(request(),[
             'Comment'=>'required',
               ],[


         ]);


         $data['Product']=request('Product');
         $data['User']=auth()->guard('client')->user()->id;
         $data['Comment']=request('Comment');
         $data['Date']=date('Y-m-d');
         Comments::create($data);

             session()->flash('success',trans('admin.Thanks'));
             return back();

    }

      public function DeleteComment($id){

            $del=Comments::find($id);
            $del->delete();
            session()->flash('error',trans('admin.Deleted'));
            return back();

}

     public function EditComment(){

        $data= $this->validate(request(),[
             'Comment'=>'required',
               ],[


         ]);


         Comments::where('id',request('ID'))->update(['Comment'=>request('Comment')]);

             session()->flash('success',trans('admin.Updated'));
             return back();

    }


    //Rate
      public function AddRate(){

        $data= $this->validate(request(),[
             'Rate'=>'required',
               ],[


         ]);


         $data['Product']=request('Product');
         $data['User']=auth()->guard('client')->user()->id;
         $data['Rate']=request('Rate');
         Rate::create($data);

        $count=Rate::where('Product',request('Product'))->count();
        $Sumcount=Rate::where('Product',request('Product'))->get()->sum('Rate');

          $result= $Sumcount / $count ;
          Products::where('id',request('Product'))->update(['rate'=>$result]);

             session()->flash('success',trans('admin.Thanks'));
             return back();

    }

      public function EditRate(){

        $data= $this->validate(request(),[
             'Rate'=>'required',
               ],[


         ]);


         $data['Rate']=request('Rate');
         Rate::where('id',request('ID'))->update($data);

        $count=Rate::where('Product',request('Product'))->count();
        $Sumcount=Rate::where('Product',request('Product'))->get()->sum('Rate');

          $result= $Sumcount / $count ;
          Products::where('id',request('Product'))->update(['rate'=>$result]);

             session()->flash('success',trans('admin.Thanks'));
             return back();

    }


    //Cart
     public function AddToCart(){

         // Validate required fields
         if(!request('Product')) {
             session()->flash('error', 'Product ID is required');
             return back();
         }

         if(!request('qty') || request('qty') < 1) {
             session()->flash('error', 'Valid quantity is required');
             return back();
         }

         if(request('qty') > request('AVQTY')){
                 session()->flash('error',trans('admin.QtyMoreAvQty'));
             return back();
         }

         $qty=request('qty');
         $Price=request('Price') ?? 0;
         $pro =Products::find(request('Product'));

         if(!$pro) {
             session()->flash('error', 'Product not found');
             return back();
         }

         $Name=$pro->P_Ar_Name;
         $OtherName=$pro->P_En_Name;

         // Handle country session safely
         $Ses=Countris::find(session()->get('ChangeCountryy'));
         $store = $Ses ? $Ses->Store : 'default';

         try {
             Cart::add([
                 'id' => $pro->id,
                 'name' => $Name,
                 'qty' => $qty,
                 'price' => $Price,
                 'weight' => 0,
                 'tax' => 0,
                 'options' => [
                     'image' => $pro->Image,
                     'store' => $store,
                     'code' => request('P_Code') ?? $pro->P_Code ?? 'N/A',
                     'AvQty' => request('AVQTY'),
                     'other_name' => $OtherName
                 ]
             ]);

             session()->flash('success', app()->getLocale() == 'ar' ? 'تم إضافة المنتج للسلة بنجاح!' : 'Product added to cart successfully!');
             return redirect('CartSite');

         } catch (\Exception $e) {
             \Log::error('AddToCart Error: ' . $e->getMessage());
             session()->flash('error', app()->getLocale() == 'ar' ? 'فشل في إضافة المنتج للسلة. حاول مرة أخرى.' : 'Failed to add product to cart. Please try again.');
             return back();
         }
    }

     public function CartSite(){

        $Carts=Cart::content();
         $x=0;

         return view('site.Cart',[
             'Carts'=>$Carts,
             'x'=>$x,
         ]);
    }

    public function DeleteCart($id){
          Cart::update($id,0);
           return back();
     }

    public function UpdateCart(){

         $qty=request('qty');
         $AvQty=request('AvQty');
         $rowId=request('RowID');
       for($i=0; $i < count($rowId) ; $i++){
           if($qty[$i] > $AvQty[$i]){
               session()->flash('error',trans('admin.QtyMoreAvQty'));
             return back();
           }else{
           Cart::update($rowId[$i],$qty[$i]);
           }
       }

         return back();

     }

     public function UpdateCuponCode(){

        $i=CouponCode::where('Code',request('code'))->first();

         if(!empty($i->id)){

            if($i->Status == 1){

            session()->flash('error',trans('admin.Code_Expired'));
             return back();

            }elseif($i->Expire < date('Y-m-d')){

              session()->flash('error',trans('admin.Code_Expired'));
             return back();

            }else{


                    if(request('code') != NULL){

                $y=$i->Used + 1 ;

                if($y == $i->Num){

                    $Status=1;
                }else{

                    $Status=0;
                }
             CouponCode::where('id',$i->id)->update(['Used'=>$y,'Status'=>$Status]);

          }


             $x=$i->Amount;
           session()->flash('success',trans('admin.Correct_Code'));

        $Carts=Cart::content();


         return view('site.Cart',[
             'Carts'=>$Carts,
             'x'=>$x,
         ]);


            }


         }else{

              session()->flash('error',trans('admin.Wrong_Code'));
             return back();

         }





     }

    //CheckoutPage
      public function Checkout(){

         try {
             $Carts=Cart::content();
         } catch (\Exception $e) {
             \Log::error('Cart content error: ' . $e->getMessage());
             $Carts = collect(); // Return empty collection if Cart fails
         }

         // Check if cart is empty and redirect if needed
         if($Carts->count() == 0){
             session()->flash('error', trans('admin.NoProductsInCart'));
             return redirect('/');
         }

          if(!empty(auth()->guard('client')->user()->id)){
    $Addresses=Addressses::where('Customer',auth()->guard('client')->user()->id)->get();
          }else{
             $Addresses=collect(); // Return empty collection instead of empty string
          }
          $x=session()->get('CODE') ?? 0; // Provide default value if session is null
        $Ses=Countris::find(session()->get('ChangeCountryy'));

          // Handle case where country session is not set or country not found
          if($Ses){
              $Governrates=Governrate::where('Country',$Ses->id)->get();
          }else{
              $Governrates=collect(); // Return empty collection if no country is set
          }
         return view('site.Checkout',[
             'x'=>$x,
             'Addresses'=>$Addresses,
             'Carts'=>$Carts,
             'Governrates'=>$Governrates,
         ]);
    }

      public function ChangeAddressSite(Request $request) {


           $id = $request->get('Address');

          $states=[];

     $adress=Addressses::find($id);
 $states += ['Address_Name' => $adress->Address_Name,'Special_MarkAdd'=>$adress->Special_Mark,'StreetAdd'=>$adress->Street,'BulidingAdd'=>$adress->Buliding,'FloorAdd'=>$adress->Floor,'FlatAdd'=>$adress->Flat,'LocationAdd'=>$adress->Location,'Address_DetailsAdd'=>$adress->Details,'Gov'=>$adress->Governrate,'GovName'=>$adress->Governrate()->first()->Arabic_Name,'Cit'=>$adress->City,'CitName'=>$adress->City()->first()->Arabic_Name , 'Pla'=>$adress->Place,'PlaName'=>$adress->Place()->first()->Arabic_Name];


       return response()->json($states);

    }

    //Orders
      public function PlaceOrder(){
 $Carts=Cart::content();
          if(!empty(auth()->guard('client')->user()->id)){
              $Client=AcccountingManual::where('id',auth()->guard('client')->user()->Account)->first();
          }else{

              $Client=AcccountingManual::where('Name','عميل نقدي')->first();
          }

          $TotQty=0;

          foreach($Carts as $cart){
              $TotQty += $cart->qty ;
          }

             $Ses=Countris::find(session()->get('ChangeCountryy'));

        $DEEF=SalesDefaultData::orderBy('id','desc')->first();


        if($DEEF->ECommercceSaleType == 1){

           $HOLDD=1;
        }else{
          $HOLDD=0;
        }



        $ID = DB::table('sales_orders')->insertGetId(
        array(
            'Date' => date('Y-m-d'),
            'Draw' => $Ses->Coin()->first()->Draw,
            'Payment_Method' => request('Payment_Method'),
            'Status' => request('Status'),
            'Refernce_Number' => null,
            'Note' => null,
            'Product_Numbers' => Cart::content()->count(),
            'Total_Qty' => $TotQty,
            'Total_Discount' => 0,
            'Total_BF_Taxes' => request('Total_Price'),
            'Total_Taxes' => 0,
            'Total_Price' => request('Total_Price'),
            'Pay' => request('Total_Price'),
            'CuponCode' => request('CuponCode'),
            'Shipping' => request('Shipping'),
            'Delivery_Status' => 0,
            'Order_Type' => 1,
            'Name' => request('Name'),
            'Email' => request('Email'),
            'Phone' => request('Phone'),
            'OtherPhone' => request('OtherPhone'),
            'Address_Name' => request('Address_Name'),
            'Special_MarkAdd' => request('Special_MarkAdd'),
            'StreetAdd' => request('StreetAdd'),
            'BulidingAdd' => request('BulidingAdd'),
            'FloorAdd' => request('FloorAdd'),
            'FlatAdd' => request('FlatAdd'),
            'Governrate' => request('Governrate'),
            'City' => request('City'),
            'Place' => request('Place'),
            'LocationAdd' => request('LocationAdd'),
            'Address_DetailsAdd' => request('Address_DetailsAdd'),
            'Safe' => $Ses->Safe,
            'Later_Due' => null,
            'Sale_Date' => null,
            'ToSales' => 0,
            'Client' => $Client->id,
            'Delegate' =>null,
            'Executor' =>null,
            'Store' => $Ses->Store,
            'Coin' => $Ses->Coin,
            'Cost_Center' =>null,
            'presenter' => null,
            'annual_interest' =>null,
            'monthly_installment' => null,
            'Years_Number' =>null,
            'total' => null,
            'Quote' => null,
            'installment_Num' => null,
            'Date_First_installment' => null,
            'User' => 1,
            'Time' => date("h:i:s a", time()),
            'Delegate_Recived' =>0,
            'Delegate_Recived_Time' =>null,
            'To_Sales_Time' =>null,
            'Cancel_Order' =>0,
            'Cancel_Order_Time' =>null,
            'Hold_Qty' =>$HOLDD,

        )
    );



                           $c= DB::select("SELECT last_value FROM sales_orders_arr_seq");
      $f=array_shift($c);
      $z=end($f);

        $CodeT=$z;



                   $notii['Date']=date('Y-m-d');
         $notii['Status']=0;
         $notii['Noti_Ar_Name']='طلب مبيعات متجر';
         $notii['Noti_En_Name']='Store Online Order';
         $notii['Type']='طلب مبيعات متجر';
  $notii['TypeEn']='Store Online Order';
         $notii['Type_Code']=$CodeT;
         $notii['Emp']= null;
         $notii['Client']=$Client->id;
         $notii['Product']=null;
         $notii['Store']=$Ses->Store;
         $notii['Safe']=$Ses->Safe;
         Notifications::create($notii);

                notify()->success(trans('admin.Online_Order'));



             if($DEEF->ECommercceSaleType == 0){

             foreach($Carts as $cart){

                   $QTY=ProductsQty::
                where('Product',$cart->id)
                    ->where('Store',$Ses->Store)
                    ->where('P_Code',$cart->options->code)
                    ->first();


        if(empty($QTY)){

               $QTY=ProductsQty::
                where('Product',$cart->id)
                    ->where('Store',$Ses->Store)
                    ->where('PP_Code',$cart->options->code)
                    ->first();

            if(empty($QTY)){
               $QTY=ProductsQty::
                where('Product',$cart->id)
                    ->where('Store',$Ses->Store)
                    ->where('PPP_Code',$cart->options->code)
                    ->first();

                  if(empty($QTY)){
                $QTY=ProductsQty::
                where('Product',$cart->id)
                    ->where('Store',$Ses->Store)
                    ->where('PPPP_Code',$cart->options->code)
                    ->first();

                  }


            }


        }

                // Check if QTY was found, if not skip this cart item or handle error
                if(empty($QTY)){
                    \Log::error('ProductsQty not found for cart item', [
                        'product_id' => $cart->id,
                        'store' => $Ses->Store,
                        'code' => $cart->options->code ?? 'N/A'
                    ]);

                    // Skip this cart item and continue with next one
                    continue;
                }

                $uu['Product_Code']=$QTY->P_Code;
                $uu['P_Ar_Name']=$QTY->P_Ar_Name;
                $uu['P_En_Name']=$QTY->P_En_Name;
                $uu['V_Name']=$QTY->V_Name;
                $uu['VV_Name']=$QTY->VV_Name;
                $uu['Original_Qty']=$cart->qty;
                $uu['Qty']=$cart->qty;
                $uu['AvQty']=$cart->options->AvQty;
                $uu['Price']=$cart->price;
                $uu['Discount']=0;
                $uu['Tax']=1;
                $uu['Total_Bf_Tax']=$cart->total;
                $uu['Total_Tax']=0;
                $uu['Total']=$cart->total;
                $uu['Store']=$Ses->Store;
                $uu['Product']=$cart->id;
                $uu['Unit']=$QTY->Unit;
                $uu['Exp_Date']=null;
                $uu['SalesOrder']=$ID;
                $uu['V1']=$QTY->V1;
                $uu['V2']=$QTY->V2;
                $uu['Patch_Number']=null;
               ProductSalesOrder::create($uu);



            }

             }else{


                foreach($Carts as $cart){

                   $QTY=ProductsQty::
                where('Product',$cart->id)
                    ->where('Store',$Ses->Store)
                    ->where('P_Code',$cart->options->code)
                    ->first();


        if(empty($QTY)){

               $QTY=ProductsQty::
                where('Product',$cart->id)
                    ->where('Store',$Ses->Store)
                    ->where('PP_Code',$cart->options->code)
                    ->first();

            if(empty($QTY)){
               $QTY=ProductsQty::
                where('Product',$cart->id)
                    ->where('Store',$Ses->Store)
                    ->where('PPP_Code',$cart->options->code)
                    ->first();

                  if(empty($QTY)){
                $QTY=ProductsQty::
                where('Product',$cart->id)
                    ->where('Store',$Ses->Store)
                    ->where('PPPP_Code',$cart->options->code)
                    ->first();

                  }


            }


        }

                // Check if QTY was found, if not skip this cart item or handle error
                if(empty($QTY)){
                    \Log::error('ProductsQty not found for cart item', [
                        'product_id' => $cart->id,
                        'store' => $Ses->Store,
                        'code' => $cart->options->code ?? 'N/A'
                    ]);

                    // Skip this cart item and continue with next one
                    continue;
                }

                $uu['Product_Code']=$QTY->P_Code;
                $uu['P_Ar_Name']=$QTY->P_Ar_Name;
                $uu['P_En_Name']=$QTY->P_En_Name;
                $uu['V_Name']=$QTY->V_Name;
                $uu['VV_Name']=$QTY->VV_Name;
                $uu['Original_Qty']=$cart->qty;
                $uu['Qty']=$cart->qty;
                $uu['AvQty']=$cart->options->AvQty;
                $uu['Price']=$cart->price;
                $uu['Discount']=0;
                $uu['Tax']=1;
                $uu['Total_Bf_Tax']=$cart->total;
                $uu['Total_Tax']=0;
                $uu['Total']=$cart->total;
                $uu['Store']=$Ses->Store;
                $uu['Product']=$cart->id;
                $uu['Unit']=$QTY->Unit;
                $uu['Exp_Date']=null;
                $uu['SalesOrder']=$ID;
                $uu['V1']=$QTY->V1;
                $uu['V2']=$QTY->V2;
                $uu['Patch_Number']=null;
               ProductSalesOrder::create($uu);



              $storTo=Stores::find(20);
    $FROMSTORE=$Ses->Store;
    $TOSTORE=$storTo->id;

                   $Quantity =ProductsQty::
                where('Store',$FROMSTORE)
                ->where('Product',$cart->id)
                ->where('P_Code',$QTY->P_Code)
                ->first();

if(empty($Quantity)){

  $Quantity =ProductsQty::
                where('Store',$FROMSTORE)
                ->where('Product',$cart->id)
                ->where('PP_Code',$QTY->P_Code)
                ->first();

if(empty($Quantity)){

  $Quantity =ProductsQty::
                where('Store',$FROMSTORE)
                ->where('Product',$cart->id)
                ->where('PPP_Code',$QTY->P_Code)
                ->first();


if(empty($Quantity)){

  $Quantity =ProductsQty::
                where('Store',$FROMSTORE)
                ->where('Product',$cart->id)
                ->where('PPPP_Code',$QTY->P_Code)
                ->first();

}



}






}



              $unit=ProductUnits::where('Unit',$QTY->Unit)->where('Product',$cart->id)->first();
                 $qq= $unit->Rate * $cart->qty ;


                $QuantityTo =ProductsQty::
                where('Store',$TOSTORE)
                ->where('Product',$cart->id)
                ->where('P_Code',$QTY->P_Code)
                ->first();

if(empty($QuantityTo)){

  $QuantityTo =ProductsQty::
                where('Store',$TOSTORE)
                ->where('Product',$cart->id)
                ->where('PP_Code',$QTY->P_Code)
                ->first();

if(empty($QuantityTo)){

  $QuantityTo =ProductsQty::
                where('Store',$TOSTORE)
                ->where('Product',$cart->id)
                ->where('PPP_Code',$QTY->P_Code)
                ->first();


if(empty($QuantityTo)){

  $QuantityTo =ProductsQty::
                where('Store',$TOSTORE)
                ->where('Product',$cart->id)
                ->where('PPPP_Code',$QTY->P_Code)
                ->first();

}



}






}


                 $prooooo=Products::find($cart->id);

                 $plow=ProductUnits::where('Product',$cart->id)->where('Rate',1)->first();
                 $unit=ProductUnits::where('Product',$cart->id)->where('Unit',$QTY->Unit)->first();

       $purchs=ProductsPurchases::where('Product',$cart->id)->where('SmallCode',$plow->Barcode)->where('Store',$FROMSTORE)->get()->sum('Total_Bf_Tax');
        $countPurchs=ProductsPurchases::where('Product',$cart->id)->where('SmallCode',$plow->Barcode)->where('Store',$FROMSTORE)->get()->sum('SmallQty');


               $storesTransfer=ProductsStoresTransfers::where('Product',$cart->id)->where('SmallCode',$plow->Barcode)->where('To_Store',$FROMSTORE)->get()->sum('Total');

         $storesTransferCount=ProductsStoresTransfers::where('Product',$cart->id)->where('SmallCode',$plow->Barcode)->where('To_Store',$FROMSTORE)->get()->sum('SmallTrans_Qty');

    $purchsStart=ProductsStartPeriods::where('Product',$cart->id)->where('SmallCode',$plow->Barcode)->where('Store',$FROMSTORE)->get()->sum('Total');

    $countStart=ProductsStartPeriods::where('Product',$cart->id)->where('SmallCode',$plow->Barcode)->where('Store',$FROMSTORE)->get()->sum('SmallQty');

           $OUTCOME=OutcomManufacturingModel::where('Product',$cart->id)->where('Store',$FROMSTORE)->where('SmallCode',$plow->Barcode)->get()->sum('Cost');

$countOUTCOME=OutcomManufacturingModel::where('Product',$cart->id)->where('Store',$FROMSTORE)->where('SmallCode',$plow->Barcode)->get()->sum('SmallQty');


     $Collect=$purchs + $purchsStart + $storesTransfer + $OUTCOME;
            $CollectCount=$countPurchs + $countStart + $storesTransferCount + $countOUTCOME ;

       if($CollectCount != 0){
    $ty= $Collect /  $CollectCount ;
                }else{

                   $ty= $Collect;
                }

         $newqty=$Quantity->Qty -  $qq ;

                 if($ty != 0){
                   $in=0;
         $out=$qq * $ty ;
         $current=$newqty * $ty ;
                }else{

             $in=0;
         $out=$qq * 1;
         $current=$newqty * 1;

                }


            $newQQty= $unit->Rate * $cart->qty ;
      $ty=$this->AverageCost($cart->total,$newQQty,$cart->id,$plow->Barcode,$FROMSTORE);
                $CostTotalSale=$ty * $newQQty;
           $lastOperation=ProductMoves::orderBy('id','desc')->where('P_Code',$plow->Barcode)->where('Product',$cart->id)->where('Store',$FROMSTORE)->first();



         $in=0;
         $out=$CostTotalSale;
            $current=($lastOperation->Current - $newQQty) *  $ty;


                  if(!empty($Quantity)){
                   $old=$Quantity->Qty - $qq ;
                    $cur=$old * $ty ;
                      ProductsQty::where('id',$Quantity->id)->update(['Qty'=>$old,'Price'=>$ty, 'TotalCost'=>$cur]);

                }



                                           //Fifo From
       $def=StoresDefaultData::orderBy('id','desc')->first();
if($def->Cost_Price == 2){

                     $fifo =FifoQty::orderBy('id','asc')
                ->where('Store',$FROMSTORE)
                ->where('Product',$cart->id)
                ->where('P_Code',$QTY->P_Code)
              ->where('Qty','!=',0)
                ->first();

                  if(empty($fifo)){

  $fifo =FifoQty::orderBy('id','asc')
                ->where('Store',$FROMSTORE)
                ->where('Product',$cart->id)
                ->where('PP_Code',$QTY->P_Code)
              ->where('Qty','!=',0)
                ->first();

if(empty($fifo)){

  $fifo =FifoQty::orderBy('id','asc')
                ->where('Store',$FROMSTORE)
                ->where('Product',$cart->id)
                ->where('PPP_Code',$QTY->P_Code)
              ->where('Qty','!=',0)
                ->first();


if(empty($fifo)){

  $fifo =FifoQty::orderBy('id','asc')
                ->where('Store',$FROMSTORE)
                ->where('Product',$cart->id)
                ->where('PPPP_Code',$QTY->P_Code)
              ->where('Qty','!=',0)
                ->first();

}

}

}



    if(!empty($fifo)){

        if($fifo->Qty >= $cart->qty){



                   $unit=ProductUnits::where('Unit',$QTY->Unit)->where('Product',$cart->id)->first();

           $qq= $unit->Rate * $cart->qty ;

           $newqty=$fifo->Qty -  $qq ;



               FifoQty::where('id',$fifo->id)->update(['Qty'=>$newqty]);


        }else{


        $resdiualQty=$cart->qty - $fifo->Qty ;

              $unit=ProductUnits::where('Unit',$QTY->Unit)->where('Product',$cart->id)->first();

           $qq= $unit->Rate * $fifo->Qty ;

           $newqty=$fifo->Qty -  $qq ;



               FifoQty::where('id',$fifo->id)->update(['Qty'=>$newqty]);



     $ResdiualCost=$this->FifoStoreQty($fifo->Store,$fifo->Product,$fifo->P_Code,$fifo->id,$fifo->Purchases_Date,$resdiualQty,$QTY->Unit);



        }


    }




    }






                 //Fifo TO
       $def=StoresDefaultData::orderBy('id','desc')->first();
if($def->Cost_Price == 2){
                     $fifo =FifoQty::
                where('Store',$TOSTORE)
                ->where('Product',$cart->id)
                ->where('P_Code',$QTY->P_Code)
                ->where('Purchases_Date',date('Y-m-d'))
                ->first();

                  if(empty($fifo)){

  $fifo =FifoQty::
                where('Store',$TOSTORE)
                ->where('Product',$cart->id)
                ->where('PP_Code',$QTY->P_Code)
                      ->where('Purchases_Date',date('Y-m-d'))
                ->first();

if(empty($fifo)){

  $fifo =FifoQty::
                where('Store',$TOSTORE)
                ->where('Product',$cart->id)
                ->where('PPP_Code',$QTY->P_Code)
                      ->where('Purchases_Date',date('Y-m-d'))
                ->first();


if(empty($fifo)){

  $fifo =FifoQty::
                where('Store',$TOSTORE)
                ->where('Product',$cart->id)
                ->where('PPPP_Code',$QTY->P_Code)
                      ->where('Purchases_Date',date('Y-m-d'))
                ->first();

}

}

}



    if(!empty($fifo)){

            $unit=ProductUnits::where('Unit',$QTY->Unit)->where('Product',$cart->id)->first();

           $qq= $unit->Rate *  $cart->qty ;

           $newqty=$fifo->Qty +  $qq ;

               FifoQty::where('id',$fifo->id)->update(['Qty'=>$newqty,'Original_Qty'=>$newqty]);

    }else{





      $pqty['P_Ar_Name']=$QTY->P_Ar_Name;
                 $pqty['Exp_Date']=null;
                    $pqty['P_En_Name']=$QTY->P_En_Name;
                    $pqty['Qty']= $cart->qty * $plow->Rate;
                    $pqty['Original_Qty']= $cart->qty * $plow->Rate;
                    $pqty['Cost_Price']=$cart->price;
                    $pqty['Store']=$TOSTORE;
                    $pqty['Unit']=$QTY->Unit;
                    $pqty['Low_Unit']=$plow->Unit;
                    $pqty['Product']=$cart->id;

           $prooooo=Products::find($cart->id);
                        $pqty['SearchCode1']=$prooooo->SearchCode1;
                    $pqty['SearchCode2']=$prooooo->SearchCode2;

                    $pqty['V1']=$QTY->V1;
                    $pqty['V2']=$QTY->V2;
                    $pqty['V_Name']=$QTY->V_Name;
                    $pqty['VV_Name']=$QTY->VV_Name;
                    $pqty['P_Code']=$QTY->P_Code;


  $proooooStore=Stores::find($TOSTORE);
   $pqty['Group']=$prooooo->Group;
                    $pqty['Brand']=$prooooo->Brand;
                    $pqty['Branch']=$proooooStore->Branch;
                    $pqty['Purchases_Date']=date('Y-m-d');
              FifoQty::create($pqty);













    }




    }








          $move['Date']=date('Y-m-d');
          $move['Type']='تحويل مخازن لامر بيع';
          $move['TypeEn']='Stores Transfer for Sales Order';
          $move['Bill_Num']=$CodeT;
          $move['Incom']=0;
          $move['Outcom']=$qq;
          $move['Current']=$newqty;
          $move['CostIn']=number_format((float)abs($in), 2, '.', '');
          $move['CostOut']=number_format((float)abs($out), 2, '.', '');
          $move['CostCurrent']=number_format((float)abs($current), 2, '.', '');
          $move['P_Ar_Name']=$QTY->P_Ar_Name;
          $move['P_En_Name']=$QTY->P_En_Name;
          $move['P_Code']=$QTY->P_Code;
          $move['Unit']=$QTY->Unit;
          $move['QTY']=$cart->qty;
          $move['Group']=$prooooo->Group;
          $move['Store']=$FROMSTORE;
          $move['Product']=$cart->id;
          $move['V1']=$QTY->V1;
          $move['V2']=$QTY->V2;
          $move['User']=1;
      $Sro=Stores::find($FROMSTORE);

                   $move['Brand']=$prooooo->Brand;
          $move['Safe']=null;
          $move['Branch']=$Sro->Branch;
          $move['SalePrice']=null;
          $move['ProductPrice']=null;
       $move['Delegate']=null;
          $move['Payment_Method']=null;
              ProductMoves::create($move);



        if(!empty($QuantityTo)){
              $prooooo=Products::find($cart->id);
            $New=$QuantityTo->Qty + $qq ;


                $plow=ProductUnits::where('Product',$cart->id)->where('Rate',1)->first();
                 $unit=ProductUnits::where('Product',$cart->id)->where('Unit',$QTY->Unit)->first();

        $purchs=ProductsPurchases::where('Product',$cart->id)->where('SmallCode',$plow->Barcode)->where('Store',$TOSTORE)->get()->sum('Total_Bf_Tax');
        $countPurchs=ProductsPurchases::where('Product',$cart->id)->where('SmallCode',$plow->Barcode)->where('Store',$TOSTORE)->get()->sum('SmallQty');


         $storesTransfer=ProductsStoresTransfers::where('Product',$cart->id)->where('SmallCode',$plow->Barcode)->where('To_Store',$TOSTORE)->get()->sum('Total');

         $storesTransferCount=ProductsStoresTransfers::where('Product',$cart->id)->where('SmallCode',$plow->Barcode)->where('To_Store',$TOSTORE)->get()->sum('SmallTrans_Qty');


    $purchsStart=ProductsStartPeriods::where('Product',$cart->id)->where('SmallCode',$plow->Barcode)->where('Store',$TOSTORE)->get()->sum('Total');

    $countStart=ProductsStartPeriods::where('Product',$cart->id)->where('SmallCode',$plow->Barcode)->where('Store',$TOSTORE)->get()->sum('SmallQty');

            $OUTCOME=OutcomManufacturingModel::where('Product',$cart->id)->where('Store',$TOSTORE)->where('SmallCode',$plow->Barcode)->get()->sum('Cost');

$countOUTCOME=OutcomManufacturingModel::where('Product',$cart->id)->where('Store',$TOSTORE)->where('SmallCode',$plow->Barcode)->get()->sum('SmallQty');


     $Collect=$purchs + $purchsStart + $storesTransfer + $OUTCOME;
            $CollectCount=$countPurchs + $countStart + $storesTransferCount + $countOUTCOME ;

       if($CollectCount != 0){
    $ty= $Collect /  $CollectCount ;
                }else{

                   $ty= $Collect;
                }


          $newqty=$QuantityTo->Qty +  $qq ;
            if($ty != 0){
                   $in=$qq * $ty;
         $out=0;
         $current=$newqty * $ty;
                }else{

             $in=$qq * 1;
         $out=0;
         $current=$newqty * 1;

                }


       $newQQty=$New;
            $ty=$this->AverageCostTwo($cart->total,$newQQty,$cart->id,$plow->Barcode,$TOSTORE);


           $lastOperation=ProductMoves::orderBy('id','desc')->where('P_Code',$plow->Barcode)->where('Product',$cart->id)->where('Store',$$TOSTORE)->first();



         $in=$cart->total;
         $out=0;
                if(!empty($lastOperation)){
         $current=$lastOperation->CostCurrent + $cart->total;
                }else{
            $current= $cart->total;
                }




           $cur=$New * $ty ;
  ProductsQty::where('id',$QuantityTo->id)->update(['Qty'=>$New,'Price'=>$ty, 'TotalCost'=>$cur]);



          $move['Date']=date('Y-m-d');
          $move['Type']='تحويل مخازن لامر بيع';
          $move['TypeEn']='Stores Transfer for Sales Order';
          $move['Bill_Num']=$CodeT;
          $move['Incom']=$qq;
          $move['Outcom']=0;
          $move['Current']=$newqty;
          $move['CostIn']=number_format((float)abs($in), 2, '.', '');
          $move['CostOut']=number_format((float)abs($out), 2, '.', '');
          $move['CostCurrent']=number_format((float)abs($current), 2, '.', '');
          $move['P_Ar_Name']=$QTY->P_Ar_Name;
          $move['P_En_Name']=$QTY->P_En_Name;
          $move['P_Code']=$QTY->P_Code;
          $move['Unit']=$QTY->Unit;
          $move['QTY']=$cart->qty;
          $move['Group']=$prooooo->Group;;
          $move['Store']=$TOSTORE;
          $move['Product']=$cart->id;
          $move['V1']=$QTY->V1;
          $move['V2']=$QTY->V2;
          $move['User']=1;
             $Sro=Stores::find($TOSTORE);

                   $move['Brand']=$prooooo->Brand;
          $move['Safe']=null;
          $move['Branch']=$Sro->Branch;
          $move['SalePrice']=null;
          $move['ProductPrice']=null;
              $move['Delegate']=null;
          $move['Payment_Method']=null;
              ProductMoves::create($move);


        }else{

               $plow=ProductUnits::where('Product',$cart->id)->where('Rate',1)->first();
                 $unit=ProductUnits::where('Product',$cart->id)->where('Unit',$QTY->Unit)->first();

        $purchs=ProductsPurchases::where('Product',$cart->id)->where('SmallCode',$plow->Barcode)->where('Store',$TOSTORE)->get()->sum('Total_Bf_Tax');
        $countPurchs=ProductsPurchases::where('Product',$cart->id)->where('SmallCode',$plow->Barcode)->where('Store',$TOSTORE)->get()->sum('SmallQty');


         $storesTransfer=ProductsStoresTransfers::where('Product',$cart->id)->where('SmallCode',$plow->Barcode)->where('To_Store',$TOSTORE)->get()->sum('Total');

         $storesTransferCount=ProductsStoresTransfers::where('Product',$cart->id)->where('SmallCode',$plow->Barcode)->where('To_Store',$TOSTORE)->get()->sum('SmallTrans_Qty');


    $purchsStart=ProductsStartPeriods::where('Product',$cart->id)->where('SmallCode',$plow->Barcode)->where('Store',$TOSTORE)->get()->sum('Total');

    $countStart=ProductsStartPeriods::where('Product',$cart->id)->where('SmallCode',$plow->Barcode)->where('Store',$TOSTORE)->get()->sum('SmallQty');

            $OUTCOME=OutcomManufacturingModel::where('Product',$cart->id)->where('Store',$TOSTORE)->where('SmallCode',$plow->Barcode)->get()->sum('Cost');

$countOUTCOME=OutcomManufacturingModel::where('Product',$cart->id)->where('Store',$TOSTORE)->where('SmallCode',$plow->Barcode)->get()->sum('SmallQty');


     $Collect=$purchs + $purchsStart + $storesTransfer + $OUTCOME;
            $CollectCount=$countPurchs + $countStart + $storesTransferCount + $countOUTCOME ;

       if($CollectCount != 0){
    $ty= $Collect /  $CollectCount ;
                }else{

                   $ty= $Collect;
                }


          $newqty=$qq ;
            if($ty != 0){
                   $in=$qq * $ty;
         $out=0;
         $current=$newqty * $ty;
                }else{

             $in=$qq * 1;
         $out=0;
         $current=$newqty * 1;

                }


              $id_store = DB::table('products_stores')->insertGetId(

        array(

            'P_Ar_Name' => $QTY->P_Ar_Name,
            'P_En_Name' => $QTY->P_En_Name,
            'P_Code' =>    $QTY->P_Code,
            'Exp_Date' => null,
            'Product' => $cart->id,
            'Store' =>$TOSTORE,
            'V1' => $QTY->V1,
            'V2' => $QTY->V2,
            'V_Name' => null,
            'VV_Name' => null,

        )
    );




         $plow=ProductUnits::where('Product',  $cart->id)->where('Rate',1)->first();

                    $pqty['P_Ar_Name']=$QTY->P_Ar_Name;
                    $pqty['P_En_Name']=$QTY->P_En_Name;
                    $pqty['Qty']=$qq;
                    $pqty['Price']= $cart->price;
                    $pqty['Pro_Stores']=$id_store;
                    $pqty['Store']=$TOSTORE;
                    $pqty['Unit']=$QTY->Unit;
                    $pqty['Low_Unit']=$plow->Unit;
                    $pqty['Product']=$cart->id;
                     $pqty['V1']=$QTY->V1;
                    $pqty['V2']=$QTY->V2;
                    $pqty['V_Name']=null;
                    $pqty['VV_Name']=null;


               $prooooo=Products::find($cart->id);
        $pqty['SearchCode1']=$prooooo->SearchCode1;
                    $pqty['SearchCode2']=$prooooo->SearchCode2;
      if($prooooo->P_Type == 'Serial' or $prooooo->P_Type == 'Single_Variable' or $prooooo->P_Type == 'Duble_Variable'){
   $proooooStore=Stores::find($TOSTORE);
   $pqty['Group']=$prooooo->Group;
                    $pqty['Brand']=$prooooo->Brand;
                    $pqty['Branch']=$proooooStore->Branch;
                    $pqty['P_Code']= $QTY->P_Code;

                      }else{

                        $coco=array();
                $CodesPrds=ProductUnits::where('Product',$cart->id)->select('Barcode')->get();
                foreach($CodesPrds as $cco){


                    array_push($coco,$cco->Barcode);

                }

                    $pqty['P_Code']=$coco[0];

                if(!empty($coco[1])){
                     $pqty['PP_Code']=$coco[1];
                }else{
                   $pqty['PP_Code']=null;
                }

                  if(!empty($coco[2])){
                     $pqty['PPP_Code']=$coco[2];
                }else{
                   $pqty['PPP_Code']=null;
                }

                  if(!empty($coco[3])){
                     $pqty['PPPP_Code']=$coco[3];
                }else{
                   $pqty['PPPP_Code']=null;
                }
                      }


         $pqty['TotalCost']=$qq * $ty;
              ProductsQty::create($pqty);



       $newQQty=$New;
            $ty=$this->AverageCostTwo($cart->total,$newQQty,$cart->id,$plow->Barcode,$TOSTORE);


           $lastOperation=ProductMoves::orderBy('id','desc')->where('P_Code',$plow->Barcode)->where('Product',$cart->id)->where('Store',$$TOSTORE)->first();



         $in=$cart->total;
         $out=0;
                if(!empty($lastOperation)){
         $current=$lastOperation->CostCurrent + $cart->total;
                }else{
            $current= $cart->total;
                }




          $move['Date']=date('Y-m-d');
          $move['Type']='تحويل مخازن لامر بيع';
          $move['TypeEn']='Stores Transfer for Sales Order';
          $move['Bill_Num']=$CodeT;
          $move['Incom']=$qq;
          $move['Outcom']=0;
          $move['Current']=$newqty;
          $move['CostIn']=number_format((float)abs($in), 2, '.', '');
          $move['CostOut']=number_format((float)abs($out), 2, '.', '');
          $move['CostCurrent']=number_format((float)abs($current), 2, '.', '');
          $move['P_Ar_Name']=$QTY->P_Ar_Name;
          $move['P_En_Name']=$QTY->P_En_Name;
          $move['P_Code']=$QTY->P_Code;
          $move['Unit']=$QTY->Unit;
          $move['QTY']=$cart->qty;
          $move['Group']=$prooooo->Group;;
          $move['Store']=$TOSTORE;
          $move['Product']=$cart->id;
          $move['V1']=$QTY->V1;
          $move['V2']=$QTY->V2;
          $move['User']=1;
                    $Sro=Stores::find($TOSTORE);

                   $move['Brand']=$prooooo->Brand;
          $move['Safe']=null;
          $move['Branch']=$Sro->Branch;
          $move['SalePrice']=null;
          $move['ProductPrice']=null;
              $move['Delegate']=null;
          $move['Payment_Method']=null;
              ProductMoves::create($move);


        }



            }

             }


             if($DEEF->ECommercceSaleType == 1){



     $storTo=Stores::find(20);
    $FROMSTORE=$Ses->Store;
    $TOSTORE=$storTo->id;

                      $res=Journalizing::orderBy('id','desc')->first();

           if(!empty($res->Code)){

              $Code=$res->Code + 1 ;
           }else{
              $Code=1;

           }


               $JunID = DB::table('journalizings')->insertGetId(

        array(

            'Code' => $Code,
            'Type' => 'تحويل مخازن لامر بيع',
            'TypeEn' =>'Stores Transfer for Sales Order',
            'Code_Type' =>$CodeT,
            'Date' => date('Y-m-d'),
            'Draw' => $Ses->Coin()->first()->Draw,
            'Coin' => $Ses->Coin,
            'Cost_Center' => null,
            'Total_Debaitor' => request('Total_Price'),
            'Total_Creditor' => request('Total_Price'),
            'Note' => request('Note'),

        )
    );


    $Fstore=Stores::find($FROMSTORE);
    $Tstore=Stores::find($TOSTORE);

        $PRODUCTSS['Joun_ID']=$JunID;
        $PRODUCTSS['Debitor']=0;
        $PRODUCTSS['Creditor']=request('Total_Price');
        $PRODUCTSS['Account']=$Fstore->Account;
        $PRODUCTSS['Statement']=null;


         JournalizingDetails::create($PRODUCTSS);

        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=date('Y-m-d');
        $Gen['Type']='تحويل مخازن لامر بيع';
        $Gen['TypeEn']='Stores Transfer for Sales Order';
        $Gen['Debitor']=0;
        $Gen['Creditor']=request('Total_Price');
        $Gen['Statement']=null;
        $Gen['Draw']=$Ses->Coin()->first()->Draw;
        $Gen['Debitor_Coin']= $Ses->Coin()->first()->Draw * 0;
        $Gen['Creditor_Coin']=$Ses->Coin()->first()->Draw * request('Total_Price');
        $Gen['Account']=$Fstore->Account;
        $Gen['Coin']= $Ses->Coin;
        $Gen['Cost_Center']=null;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);


        $PRODUCTSS['Joun_ID']=$JunID;
        $PRODUCTSS['Debitor']=request('Total_Price');
        $PRODUCTSS['Creditor']=0;
        $PRODUCTSS['Account']=$Tstore->Account;
        $PRODUCTSS['Statement']=null;


         JournalizingDetails::create($PRODUCTSS);

        $Gen['Code']=$Code;
        $Gen['Code_Type']=$CodeT;
        $Gen['Date']=date('Y-m-d');
        $Gen['Type']= 'تحويل مخازن لامر بيع';
        $Gen['TypeEn']='Stores Transfer for Sales Order';
        $Gen['Debitor']=request('Total_Price');
        $Gen['Creditor']=0;
        $Gen['Statement']=null;
        $Gen['Draw']=$Ses->Coin()->first()->Draw;
        $Gen['Debitor_Coin']= $Ses->Coin()->first()->Draw * request('Total_Price');
        $Gen['Creditor_Coin']=$Ses->Coin()->first()->Draw * 0;
        $Gen['Account']=$Tstore->Account;
        $Gen['Coin']= $Ses->Coin;
        $Gen['Cost_Center']= null;
        $Gen['userr']= auth()->guard('admin')->user()->id;

         GeneralDaily::create($Gen);

          }


   Cart::destroy();
             session()->flash('success',trans('admin.Order_Successfully'));
          return redirect('/');


    }

    /**
     * Place order with payment gateway integration
     */
    public function PlaceOrderWithPayment()
    {
        try {
            // Validate payment method
            $paymentMethod = request('payment_method', 'cash');

            if ($paymentMethod === 'cash') {
                // Handle cash payment using existing logic
                return $this->PlaceOrder();
            }

            // For online payment, create order first then redirect to payment
            $Carts = Cart::content();

            if ($Carts->count() == 0) {
                return response()->json([
                    'success' => false,
                    'message' => trans('admin.NoProductsInCart')
                ], 400);
            }

            if (!empty(auth()->guard('client')->user()->id)) {
                $Client = AcccountingManual::where('id', auth()->guard('client')->user()->Account)->first();
            } else {
                $Client = AcccountingManual::where('Name', 'عميل نقدي')->first();
            }

            $TotQty = 0;
            foreach ($Carts as $cart) {
                $TotQty += $cart->qty;
            }

            $Ses = Countris::find(session()->get('ChangeCountryy'));
            $DEEF = SalesDefaultData::orderBy('id', 'desc')->first();

            if ($DEEF->ECommercceSaleType == 1) {
                $HOLDD = 1;
            } else {
                $HOLDD = 0;
            }

            // Create sales order with pending payment status
            $ID = DB::table('sales_orders')->insertGetId([
                'Date' => date('Y-m-d'),
                'Draw' => $Ses->Coin()->first()->Draw,
                'Payment_Method' => 'Online',
                'payment_gateway' => 'banque_misr',
                'payment_status' => 'pending',
                'Status' => 0, // Pending until payment confirmation
                'Refernce_Number' => null,
                'Note' => null,
                'Product_Numbers' => Cart::content()->count(),
                'Total_Qty' => $TotQty,
                'Total_Discount' => 0,
                'Total_BF_Taxes' => request('Total_Price'),
                'Total_Taxes' => 0,
                'Total_Price' => request('Total_Price'),
                'Pay' => request('Total_Price'),
                'CuponCode' => request('CuponCode'),
                'Shipping' => request('Shipping'),
                'Delivery_Status' => 0,
                'Order_Type' => 1,
                'Name' => request('Name'),
                'Email' => request('Email'),
                'Phone' => request('Phone'),
                'OtherPhone' => request('OtherPhone'),
                'Address_Name' => request('Address_Name'),
                'Special_MarkAdd' => request('Special_MarkAdd'),
                'StreetAdd' => request('StreetAdd'),
                'BulidingAdd' => request('BulidingAdd'),
                'FloorAdd' => request('FloorAdd'),
                'FlatAdd' => request('FlatAdd'),
                'Governrate' => request('Governrate'),
                'City' => request('City'),
                'Place' => request('Place'),
                'LocationAdd' => request('LocationAdd'),
                'Address_DetailsAdd' => request('Address_DetailsAdd'),
                'Safe' => $Ses->Safe,
                'Later_Due' => null,
                'Sale_Date' => null,
                'ToSales' => 0,
                'Client' => $Client->id,
                'Delegate' => null,
                'Executor' => null,
                'Store' => $Ses->Store,
                'Coin' => $Ses->Coin,
                'Cost_Center' => null,
                'presenter' => null,
                'annual_interest' => null,
                'monthly_installment' => null,
                'Years_Number' => null,
                'total' => null,
                'Quote' => null,
                'installment_Num' => null,
                'Date_First_installment' => null,
                'User' => 1,
                'Time' => date("h:i:s a", time()),
                'Delegate_Recived' => 0,
                'Delegate_Recived_Time' => null,
                'To_Sales_Time' => null,
                'Cancel_Order' => 0,
                'Cancel_Order_Time' => null,
                'Hold_Qty' => $HOLDD,
            ]);

            // Add order products
            foreach ($Carts as $cart) {
                $pro = Products::where('id', $cart->id)->first();

                $uu = [
                    'P_Ar_Name' => $pro->P_Ar_Name,
                    'P_En_Name' => $pro->P_En_Name,
                    'Qty' => $cart->qty,
                    'Price' => $cart->price,
                    'Total' => $cart->total,
                    'Product' => $cart->id,
                    'Sales_Order' => $ID,
                ];

                ProductSalesOrder::create($uu);
            }

            // Initiate payment with payment gateway
            $paymentController = new \App\Http\Controllers\PaymentController(
                new \App\Services\BanqueMisrPaymentService()
            );

            $paymentRequest = new \Illuminate\Http\Request([
                'sales_order_id' => $ID,
                'amount' => request('Total_Price'),
                'currency' => $Ses->Coin()->first()->Symbol ?? 'EGP',
                'payment_method' => 'CARD',
            ]);

            $paymentResponse = $paymentController->initiatePayment($paymentRequest);
            $paymentData = $paymentResponse->getData(true);

            if ($paymentData['success']) {
                // Don't destroy cart yet - wait for payment confirmation
                return response()->json([
                    'success' => true,
                    'redirect_url' => $paymentData['redirect_url'],
                    'message' => 'Redirecting to payment gateway...'
                ]);
            } else {
                // Payment initiation failed, delete the order
                SalesOrder::where('id', $ID)->delete();
                ProductSalesOrder::where('Sales_Order', $ID)->delete();

                return response()->json([
                    'success' => false,
                    'message' => 'Failed to initiate payment. Please try again.'
                ], 500);
            }

        } catch (\Exception $e) {
            \Log::error('PlaceOrderWithPayment Error: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while processing your order. Please try again.'
            ], 500);
        }
    }

    // ============ NEW WEBSITE MODULES ============

    // Categories Module
    public function CategoriesPage(){
        $Categories = Categories::orderBy('id', 'desc')->get();
        return view('admin.Website.Categories', [
            'Categories' => $Categories
        ]);
    }

    public function AddCategory(){
        $data = $this->validate(request(), [
            'Arabic_Name' => 'required|string|max:255',
            'English_Name' => 'required|string|max:255'
        ]);

        Categories::create($data);

        session()->flash('success', trans('admin.Added'));
        return back();
    }

    public function UpdateCategory($id){
        $category = Categories::findOrFail($id);

        $data = $this->validate(request(), [
            'Arabic_Name' => 'required|string|max:255',
            'English_Name' => 'required|string|max:255'
        ]);

        $category->update($data);

        session()->flash('success', trans('admin.Updated'));
        return back();
    }

    public function DeleteCategory($id){
        $category = Categories::findOrFail($id);
        $category->delete();

        session()->flash('success', trans('admin.Deleted'));
        return back();
    }

    // Gallery Module
    public function GalleryPage(){
        $Gallery = Gallery::orderBy('id', 'desc')->get();
        $Categories = Categories::orderBy('id', 'desc')->get();
        return view('admin.Website.Gallery', [
            'Gallery' => $Gallery,
            'Categories' => $Categories
        ]);
    }

    public function AddGallery(){
        $data = $this->validate(request(), [
            'Image' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
            'Arabic_Name' => 'required|string|max:255',
            'English_Name' => 'required|string|max:255',
            'Category' => 'nullable|integer|exists:categories,id',
            'Links' => 'nullable|url'
        ]);

        if(request()->hasFile('Image')){
            $image = request()->file('Image');
            $imageName = time() . '_gallery.' . $image->getClientOriginalExtension();
            $image->move(public_path('uploads/gallery'), $imageName);
            $data['Image'] = 'uploads/gallery/' . $imageName;
        }

        Gallery::create($data);

        session()->flash('success', trans('admin.Added'));
        return back();
    }

    public function UpdateGallery($id){
        $gallery = Gallery::findOrFail($id);

        $data = $this->validate(request(), [
            'Image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'Arabic_Name' => 'required|string|max:255',
            'English_Name' => 'required|string|max:255',
            'Category' => 'nullable|integer|exists:categories,id',
            'Links' => 'nullable|url'
        ]);

        if(request()->hasFile('Image')){
            // Delete old image
            if(file_exists(public_path($gallery->Image))){
                unlink(public_path($gallery->Image));
            }

            $image = request()->file('Image');
            $imageName = time() . '_gallery.' . $image->getClientOriginalExtension();
            $image->move(public_path('uploads/gallery'), $imageName);
            $data['Image'] = 'uploads/gallery/' . $imageName;
        } else {
            $data['Image'] = request('OldImage');
        }

        $gallery->update($data);

        session()->flash('success', trans('admin.Updated'));
        return back();
    }

    public function DeleteGallery($id){
        $gallery = Gallery::findOrFail($id);

        // Delete image file
        if(file_exists(public_path($gallery->Image))){
            unlink(public_path($gallery->Image));
        }

        $gallery->delete();

        session()->flash('success', trans('admin.Deleted'));
        return back();
    }

    // Services Module
    public function ServicesPage(){
        $Services = Services::orderBy('Order', 'asc')->get();
        return view('admin.Website.Services', [
            'Services' => $Services
        ]);
    }

    public function AddService(){
        $data = $this->validate(request(), [
            'Icon' => 'required|image|mimes:jpeg,png,jpg,gif|max:1024',
            'Image' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
            'Arabic_Title' => 'required|string|max:255',
            'English_Title' => 'required|string|max:255',
            'Arabic_Desc' => 'required|string',
            'English_Desc' => 'required|string',
            'Status' => 'required|boolean',
            'Order' => 'required|integer'
        ]);

        if(request()->hasFile('Icon')){
            $icon = request()->file('Icon');
            $iconName = time() . '_service_icon.' . $icon->getClientOriginalExtension();
            $icon->move(public_path('uploads/services/icons'), $iconName);
            $data['Icon'] = 'uploads/services/icons/' . $iconName;
        }

        if(request()->hasFile('Image')){
            $image = request()->file('Image');
            $imageName = time() . '_service.' . $image->getClientOriginalExtension();
            $image->move(public_path('uploads/services'), $imageName);
            $data['Image'] = 'uploads/services/' . $imageName;
        }

        Services::create($data);

        session()->flash('success', trans('admin.Added'));
        return back();
    }

    public function UpdateService($id){
        $service = Services::findOrFail($id);

        $data = $this->validate(request(), [
            'Icon' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:1024',
            'Image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'Arabic_Title' => 'required|string|max:255',
            'English_Title' => 'required|string|max:255',
            'Arabic_Desc' => 'required|string',
            'English_Desc' => 'required|string',
            'Status' => 'required|boolean',
            'Order' => 'required|integer'
        ]);

        if(request()->hasFile('Icon')){
            // Delete old icon
            if(file_exists(public_path($service->Icon))){
                unlink(public_path($service->Icon));
            }

            $icon = request()->file('Icon');
            $iconName = time() . '_service_icon.' . $icon->getClientOriginalExtension();
            $icon->move(public_path('uploads/services/icons'), $iconName);
            $data['Icon'] = 'uploads/services/icons/' . $iconName;
        } else {
            $data['Icon'] = request('OldIcon');
        }

        if(request()->hasFile('Image')){
            // Delete old image
            if(file_exists(public_path($service->Image))){
                unlink(public_path($service->Image));
            }

            $image = request()->file('Image');
            $imageName = time() . '_service.' . $image->getClientOriginalExtension();
            $image->move(public_path('uploads/services'), $imageName);
            $data['Image'] = 'uploads/services/' . $imageName;
        } else {
            $data['Image'] = request('OldImage');
        }

        $service->update($data);

        session()->flash('success', trans('admin.Updated'));
        return back();
    }

    public function DeleteService($id){
        $service = Services::findOrFail($id);

        // Delete files
        if(file_exists(public_path($service->Icon))){
            unlink(public_path($service->Icon));
        }
        if(file_exists(public_path($service->Image))){
            unlink(public_path($service->Image));
        }

        $service->delete();

        session()->flash('success', trans('admin.Deleted'));
        return back();
    }

    // Blog Module
    public function BlogPage(){
        $Blog = Blog::orderBy('created_at', 'desc')->get();
        return view('admin.Website.Blog', [
            'Blog' => $Blog
        ]);
    }

    public function AddBlog(){
        $data = $this->validate(request(), [
            'Featured_Image' => 'required|image|mimes:jpeg,png,jpg,gif|max:3072',
            'Thumbnail' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
            'Arabic_Title' => 'required|string|max:255',
            'English_Title' => 'required|string|max:255',
            'Arabic_Summary' => 'required|string',
            'English_Summary' => 'required|string',
            'Arabic_Content' => 'required|string',
            'English_Content' => 'required|string',
            'Category' => 'required|string',
            'Status' => 'required|boolean',
            'Featured' => 'required|boolean',
            'Tags' => 'nullable|string',
            'Author' => 'required|string|max:255'
        ]);

        if(request()->hasFile('Featured_Image')){
            $featured = request()->file('Featured_Image');
            $featuredName = time() . '_blog_featured.' . $featured->getClientOriginalExtension();
            $featured->move(public_path('uploads/blog'), $featuredName);
            $data['Featured_Image'] = 'uploads/blog/' . $featuredName;
        }

        if(request()->hasFile('Thumbnail')){
            $thumb = request()->file('Thumbnail');
            $thumbName = time() . '_blog_thumb.' . $thumb->getClientOriginalExtension();
            $thumb->move(public_path('uploads/blog/thumbs'), $thumbName);
            $data['Thumbnail'] = 'uploads/blog/thumbs/' . $thumbName;
        }

        Blog::create($data);

        session()->flash('success', trans('admin.Added'));
        return back();
    }

    public function UpdateBlog($id){
        $blog = Blog::findOrFail($id);

        $data = $this->validate(request(), [
            'Featured_Image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:3072',
            'Thumbnail' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'Arabic_Title' => 'required|string|max:255',
            'English_Title' => 'required|string|max:255',
            'Arabic_Summary' => 'required|string',
            'English_Summary' => 'required|string',
            'Arabic_Content' => 'required|string',
            'English_Content' => 'required|string',
            'Category' => 'required|string',
            'Status' => 'required|boolean',
            'Featured' => 'required|boolean',
            'Tags' => 'nullable|string',
            'Author' => 'required|string|max:255'
        ]);

        if(request()->hasFile('Featured_Image')){
            if(file_exists(public_path($blog->Featured_Image))){
                unlink(public_path($blog->Featured_Image));
            }

            $featured = request()->file('Featured_Image');
            $featuredName = time() . '_blog_featured.' . $featured->getClientOriginalExtension();
            $featured->move(public_path('uploads/blog'), $featuredName);
            $data['Featured_Image'] = 'uploads/blog/' . $featuredName;
        } else {
            $data['Featured_Image'] = request('OldFeatured');
        }

        if(request()->hasFile('Thumbnail')){
            if(file_exists(public_path($blog->Thumbnail))){
                unlink(public_path($blog->Thumbnail));
            }

            $thumb = request()->file('Thumbnail');
            $thumbName = time() . '_blog_thumb.' . $thumb->getClientOriginalExtension();
            $thumb->move(public_path('uploads/blog/thumbs'), $thumbName);
            $data['Thumbnail'] = 'uploads/blog/thumbs/' . $thumbName;
        } else {
            $data['Thumbnail'] = request('OldThumbnail');
        }

        $blog->update($data);

        session()->flash('success', trans('admin.Updated'));
        return back();
    }

    public function DeleteBlog($id){
        $blog = Blog::findOrFail($id);

        // Delete files
        if(file_exists(public_path($blog->Featured_Image))){
            unlink(public_path($blog->Featured_Image));
        }
        if(file_exists(public_path($blog->Thumbnail))){
            unlink(public_path($blog->Thumbnail));
        }

        $blog->delete();

        session()->flash('success', trans('admin.Deleted'));
        return back();
    }

    // Team Module
    public function TeamPage(){
        $Team = Team::orderBy('Order', 'asc')->get();
        return view('admin.Website.Team', [
            'Team' => $Team
        ]);
    }

    public function AddTeam(){
        $data = $this->validate(request(), [
            'Image' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
            'Arabic_Name' => 'required|string|max:255',
            'English_Name' => 'required|string|max:255',
            'Arabic_Position' => 'required|string|max:255',
            'English_Position' => 'required|string|max:255',
            'Email' => 'nullable|email|max:255',
            'Phone' => 'nullable|string|max:20',
            'Arabic_Bio' => 'nullable|string',
            'English_Bio' => 'nullable|string',
            'Facebook' => 'nullable|url',
            'Twitter' => 'nullable|url',
            'LinkedIn' => 'nullable|url',
            'Instagram' => 'nullable|url',
            'Status' => 'required|boolean',
            'Order' => 'required|integer'
        ]);

        if(request()->hasFile('Image')){
            $image = request()->file('Image');
            $imageName = time() . '_team.' . $image->getClientOriginalExtension();
            $image->move(public_path('uploads/team'), $imageName);
            $data['Image'] = 'uploads/team/' . $imageName;
        }

        Team::create($data);

        session()->flash('success', trans('admin.Added'));
        return back();
    }

    public function UpdateTeam($id){
        $team = Team::findOrFail($id);

        $data = $this->validate(request(), [
            'Image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'Arabic_Name' => 'required|string|max:255',
            'English_Name' => 'required|string|max:255',
            'Arabic_Position' => 'required|string|max:255',
            'English_Position' => 'required|string|max:255',
            'Email' => 'nullable|email|max:255',
            'Phone' => 'nullable|string|max:20',
            'Arabic_Bio' => 'nullable|string',
            'English_Bio' => 'nullable|string',
            'Facebook' => 'nullable|url',
            'Twitter' => 'nullable|url',
            'LinkedIn' => 'nullable|url',
            'Instagram' => 'nullable|url',
            'Status' => 'required|boolean',
            'Order' => 'required|integer'
        ]);

        if(request()->hasFile('Image')){
            if(file_exists(public_path($team->Image))){
                unlink(public_path($team->Image));
            }

            $image = request()->file('Image');
            $imageName = time() . '_team.' . $image->getClientOriginalExtension();
            $image->move(public_path('uploads/team'), $imageName);
            $data['Image'] = 'uploads/team/' . $imageName;
        } else {
            $data['Image'] = request('OldImage');
        }

        $team->update($data);

        session()->flash('success', trans('admin.Updated'));
        return back();
    }

    public function DeleteTeam($id){
        $team = Team::findOrFail($id);

        // Delete image file
        if(file_exists(public_path($team->Image))){
            unlink(public_path($team->Image));
        }

        $team->delete();

        session()->flash('success', trans('admin.Deleted'));
        return back();
    }

    // Testimonials Module
    public function TestimonialsPage(){
        $Testimonials = Testimonials::orderBy('Rating', 'desc')->get();
        return view('admin.Website.Testimonials', [
            'Testimonials' => $Testimonials
        ]);
    }

    public function AddTestimonial(){
        $data = $this->validate(request(), [
            'Image' => 'required|image|mimes:jpeg,png,jpg,gif|max:1024',
            'Arabic_Name' => 'required|string|max:255',
            'English_Name' => 'required|string|max:255',
            'Arabic_Position' => 'required|string|max:255',
            'English_Position' => 'required|string|max:255',
            'Company' => 'required|string|max:255',
            'Arabic_Testimonial' => 'required|string',
            'English_Testimonial' => 'required|string',
            'Rating' => 'required|integer|min:1|max:5',
            'Status' => 'required|boolean',
            'Featured' => 'required|boolean'
        ]);

        if(request()->hasFile('Image')){
            $image = request()->file('Image');
            $imageName = time() . '_testimonial.' . $image->getClientOriginalExtension();
            $image->move(public_path('uploads/testimonials'), $imageName);
            $data['Image'] = 'uploads/testimonials/' . $imageName;
        }

        Testimonials::create($data);

        session()->flash('success', trans('admin.Added'));
        return back();
    }

    public function UpdateTestimonial($id){
        $testimonial = Testimonials::findOrFail($id);

        $data = $this->validate(request(), [
            'Image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:1024',
            'Arabic_Name' => 'required|string|max:255',
            'English_Name' => 'required|string|max:255',
            'Arabic_Position' => 'required|string|max:255',
            'English_Position' => 'required|string|max:255',
            'Company' => 'required|string|max:255',
            'Arabic_Testimonial' => 'required|string',
            'English_Testimonial' => 'required|string',
            'Rating' => 'required|integer|min:1|max:5',
            'Status' => 'required|boolean',
            'Featured' => 'required|boolean'
        ]);

        if(request()->hasFile('Image')){
            if(file_exists(public_path($testimonial->Image))){
                unlink(public_path($testimonial->Image));
            }

            $image = request()->file('Image');
            $imageName = time() . '_testimonial.' . $image->getClientOriginalExtension();
            $image->move(public_path('uploads/testimonials'), $imageName);
            $data['Image'] = 'uploads/testimonials/' . $imageName;
        } else {
            $data['Image'] = request('OldImage');
        }

        $testimonial->update($data);

        session()->flash('success', trans('admin.Updated'));
        return back();
    }

    public function DeleteTestimonial($id){
        $testimonial = Testimonials::findOrFail($id);

        // Delete image file
        if(file_exists(public_path($testimonial->Image))){
            unlink(public_path($testimonial->Image));
        }

        $testimonial->delete();

        session()->flash('success', trans('admin.Deleted'));
        return back();
    }

    // Website Features Module
    public function WebsiteFeaturesPage(){
        $Features = WebsiteFeatures::orderBy('Order', 'asc')->get();
        return view('admin.Website.Features', [
            'Features' => $Features
        ]);
    }

    public function AddFeature(){
        $data = $this->validate(request(), [
            'Icon' => 'required|image|mimes:jpeg,png,jpg,gif|max:1024',
            'Image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'Arabic_Title' => 'required|string|max:255',
            'English_Title' => 'required|string|max:255',
            'Arabic_Desc' => 'required|string',
            'English_Desc' => 'required|string',
            'Feature_Type' => 'required|string|in:Main,Secondary,Highlight',
            'Status' => 'required|boolean',
            'Order' => 'required|integer',
            'Link_URL' => 'nullable|url',
            'Button_Text' => 'nullable|string|max:100'
        ]);

        if(request()->hasFile('Icon')){
            $icon = request()->file('Icon');
            $iconName = time() . '_feature_icon.' . $icon->getClientOriginalExtension();
            $icon->move(public_path('uploads/features/icons'), $iconName);
            $data['Icon'] = 'uploads/features/icons/' . $iconName;
        }

        if(request()->hasFile('Image')){
            $image = request()->file('Image');
            $imageName = time() . '_feature.' . $image->getClientOriginalExtension();
            $image->move(public_path('uploads/features'), $imageName);
            $data['Image'] = 'uploads/features/' . $imageName;
        }

        WebsiteFeatures::create($data);

        session()->flash('success', trans('admin.Added'));
        return back();
    }

    public function UpdateFeature($id){
        $feature = WebsiteFeatures::findOrFail($id);

        $data = $this->validate(request(), [
            'Icon' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:1024',
            'Image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'Arabic_Title' => 'required|string|max:255',
            'English_Title' => 'required|string|max:255',
            'Arabic_Desc' => 'required|string',
            'English_Desc' => 'required|string',
            'Feature_Type' => 'required|string|in:Main,Secondary,Highlight',
            'Status' => 'required|boolean',
            'Order' => 'required|integer',
            'Link_URL' => 'nullable|url',
            'Button_Text' => 'nullable|string|max:100'
        ]);

        if(request()->hasFile('Icon')){
            if(file_exists(public_path($feature->Icon))){
                unlink(public_path($feature->Icon));
            }

            $icon = request()->file('Icon');
            $iconName = time() . '_feature_icon.' . $icon->getClientOriginalExtension();
            $icon->move(public_path('uploads/features/icons'), $iconName);
            $data['Icon'] = 'uploads/features/icons/' . $iconName;
        } else {
            $data['Icon'] = request('OldIcon');
        }

        if(request()->hasFile('Image')){
            if(file_exists(public_path($feature->Image))){
                unlink(public_path($feature->Image));
            }

            $image = request()->file('Image');
            $imageName = time() . '_feature.' . $image->getClientOriginalExtension();
            $image->move(public_path('uploads/features'), $imageName);
            $data['Image'] = 'uploads/features/' . $imageName;
        } else {
            $data['Image'] = request('OldImage');
        }

        $feature->update($data);

        session()->flash('success', trans('admin.Updated'));
        return back();
    }

    public function DeleteFeature($id){
        $feature = WebsiteFeatures::findOrFail($id);

        // Delete files
        if(file_exists(public_path($feature->Icon))){
            unlink(public_path($feature->Icon));
        }
        if(file_exists(public_path($feature->Image))){
            unlink(public_path($feature->Image));
        }

        $feature->delete();

        session()->flash('success', trans('admin.Deleted'));
        return back();
    }

}
