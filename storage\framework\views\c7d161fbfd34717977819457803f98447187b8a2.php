<?php $__env->startSection('content'); ?>
<title><?php echo e(trans('admin.Gallery')); ?></title>
<main id="js-page-content" role="main" class="page-content">
   <ol class="breadcrumb page-breadcrumb">
      <li class="breadcrumb-item"><a href="javascript:void(0);"><?php echo e(trans('admin.Home')); ?></a></li>
      <li class="breadcrumb-item active"><?php echo e(trans('admin.Gallery')); ?></li>
      <li class="position-absolute pos-top pos-right d-none d-sm-block"><span class="js-get-date"></span></li>
   </ol>
   
   <style>
      .Hei{
         background: var(--theme-color);
         color: WHITE;
         padding: 10px;
         margin-bottom: 20px;
         box-shadow: 0 2px 6px 0 rgba(92, 75, 45, 0.5);
      }
      .wei{
         background: var(--theme-color);
         color: WHITE;
         padding: 10px;
         margin-bottom: 20px;
         box-shadow: 0 2px 6px 0 rgba(92, 75, 45, 0.5);
      }
      .BlogsH{
         display: flex;
         text-align: center;
         flex-direction: column;
      }
   </style>

   <div class="Sizes">
      <div class="container row">
         <div class="col-lg-12 text-center"><h2>Size Of Picture:</h2></div>
         <div class="col-lg-12 text-center Hei">
            <h2 class="BlogsH">Gallery Images</h2>
            Width: 600px ^ Height: 400px
         </div>
      </div> 
   </div>

   <!-- data entry -->
   <div class="row">
      <div class="col-lg-12">
         <div id="panel-2" class="panel">
            <div class="panel-hdr"></div>
            <div class="panel-container show">
               <span id="ex"><?php echo $__env->make('admin.layouts.messages', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?></span>     
               <div class="panel-content">
                  <ul class="nav nav-tabs" role="tablist">
                     <li class="nav-item">
                        <a class="nav-link active" data-toggle="tab" href="#tab_borders_icons-7" role="tab"><?php echo e(trans('admin.Add')); ?></a>
                     </li>
                     <li class="nav-item">
                        <a class="nav-link" data-toggle="tab" href="#tab_borders_icons-8" role="tab"><?php echo e(trans('admin.Show')); ?></a>
                     </li>
                  </ul>
                  <div class="tab-content border border-top-0 p-3">
                     <div class="tab-pane fade show active" id="tab_borders_icons-7" role="tabpanel">
                        <form action="<?php echo e(url('AddGallery')); ?>" method="post" enctype="multipart/form-data" class="form-row">
                           <?php echo csrf_field(); ?>

                           <div class="col-md-12">
                              <div class="data-def">
                                 <div class="form-row">
                                    <div class="form-group col-md-6">
                                       <label class="form-label"><?php echo e(trans('admin.Image')); ?></label>
                                       <input type="file" name="Image" class="form-control" required>
                                    </div>
                                    <div class="form-group col-md-6">
                                       <label class="form-label"><?php echo e(trans('admin.Arabic_Title')); ?></label>
                                       <input type="text" name="Arabic_Title" class="form-control" required>
                                    </div>
                                    <div class="form-group col-md-6">
                                       <label class="form-label"><?php echo e(trans('admin.English_Title')); ?></label>
                                       <input type="text" name="English_Title" class="form-control" required>
                                    </div>
                                    <div class="form-group col-md-6">
                                       <label class="form-label"><?php echo e(trans('admin.Arabic_Desc')); ?></label>
                                       <textarea name="Arabic_Desc" class="form-control" rows="3" required></textarea>
                                    </div>
                                    <div class="form-group col-md-6">
                                       <label class="form-label"><?php echo e(trans('admin.English_Desc')); ?></label>
                                       <textarea name="English_Desc" class="form-control" rows="3" required></textarea>
                                    </div>
                                 </div>
                              </div>
                           </div>
                           <div class="col-md-12">
                              <button type="submit" class="btn btn-primary"><?php echo e(trans('admin.Save')); ?></button>
                           </div>
                        </form>
                     </div>
                     
                     <div class="tab-pane fade" id="tab_borders_icons-8" role="tabpanel">
                        <table id="dt-basic-example" class="table table-bordered table-hover table-striped w-100">
                           <thead class="bg-primary-600">
                              <tr>
                                 <th><?php echo e(trans('admin.Image')); ?></th>
                                 <th><?php echo e(trans('admin.Arabic_Title')); ?></th>
                                 <th><?php echo e(trans('admin.English_Title')); ?></th>
                                 <th><?php echo e(trans('admin.Arabic_Desc')); ?></th>
                                 <th><?php echo e(trans('admin.English_Desc')); ?></th>
                                 <th><?php echo e(trans('admin.Action')); ?></th>
                              </tr>
                           </thead>
                           <tbody>
                              <?php $__currentLoopData = $Gallery; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                              <tr>
                                 <td><img src="<?php echo e(URL::to($item->Image)); ?>" style="width: 80px; height: 60px; object-fit: cover;"></td>
                                 <td><?php echo e($item->Arabic_Title); ?></td>
                                 <td><?php echo e($item->English_Title); ?></td>
                                 <td><?php echo e(Str::limit($item->Arabic_Desc, 50)); ?></td>
                                 <td><?php echo e(Str::limit($item->English_Desc, 50)); ?></td>
                                 <td>
                                    <button type="button" class="btn btn-outline-primary btn-sm" data-toggle="modal" data-target="#editModal<?php echo e($item->id); ?>">
                                       <i class="fal fa-edit"></i>
                                    </button>
                                    <a href="<?php echo e(url('DeleteGallery/'.$item->id)); ?>" class="btn btn-outline-danger btn-sm" onclick="return confirm('<?php echo e(trans('admin.AreYouSure')); ?>')">
                                       <i class="fal fa-trash"></i>
                                    </a>
                                 </td>
                              </tr>
                              <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                           </tbody>
                        </table>
                     </div>
                  </div>
               </div>
            </div>
         </div>
      </div>
   </div>

   <!-- Edit Modals -->
   <?php $__currentLoopData = $Gallery; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
   <div class="modal fade" id="editModal<?php echo e($item->id); ?>" tabindex="-1" role="dialog">
      <div class="modal-dialog modal-lg" role="document">
         <div class="modal-content">
            <div class="modal-header">
               <h5 class="modal-title"><?php echo e(trans('admin.Edit')); ?> <?php echo e(trans('admin.Gallery')); ?></h5>
               <button type="button" class="close" data-dismiss="modal">
                  <span>&times;</span>
               </button>
            </div>
            <form action="<?php echo e(url('UpdateGallery/'.$item->id)); ?>" method="post" enctype="multipart/form-data">
               <?php echo csrf_field(); ?>

               <div class="modal-body">
                  <div class="row">
                     <div class="col-md-6">
                        <div class="form-group">
                           <label><?php echo e(trans('admin.Image')); ?></label>
                           <input type="file" name="Image" class="form-control">
                           <input type="hidden" name="OldImage" value="<?php echo e($item->Image); ?>">
                        </div>
                     </div>
                     <div class="col-md-6">
                        <img src="<?php echo e(URL::to($item->Image)); ?>" class="img-fluid" style="max-height: 150px;">
                     </div>
                     <div class="col-md-6">
                        <div class="form-group">
                           <label><?php echo e(trans('admin.Arabic_Title')); ?></label>
                           <input type="text" name="Arabic_Title" value="<?php echo e($item->Arabic_Title); ?>" class="form-control" required>
                        </div>
                     </div>
                     <div class="col-md-6">
                        <div class="form-group">
                           <label><?php echo e(trans('admin.English_Title')); ?></label>
                           <input type="text" name="English_Title" value="<?php echo e($item->English_Title); ?>" class="form-control" required>
                        </div>
                     </div>
                     <div class="col-md-6">
                        <div class="form-group">
                           <label><?php echo e(trans('admin.Arabic_Desc')); ?></label>
                           <textarea name="Arabic_Desc" class="form-control" rows="3" required><?php echo e($item->Arabic_Desc); ?></textarea>
                        </div>
                     </div>
                     <div class="col-md-6">
                        <div class="form-group">
                           <label><?php echo e(trans('admin.English_Desc')); ?></label>
                           <textarea name="English_Desc" class="form-control" rows="3" required><?php echo e($item->English_Desc); ?></textarea>
                        </div>
                     </div>
                  </div>
               </div>
               <div class="modal-footer">
                  <button type="button" class="btn btn-secondary" data-dismiss="modal"><?php echo e(trans('admin.Close')); ?></button>
                  <button type="submit" class="btn btn-primary"><?php echo e(trans('admin.Update')); ?></button>
               </div>
            </form>
         </div>
      </div>
   </div>
   <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
</main>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('js'); ?>
<link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/datagrid/datatables/datatables.bundle.css')); ?>">
<script src="<?php echo e(asset('Admin/js/datagrid/datatables/datatables.bundle.js')); ?>"></script>
<script src="<?php echo e(asset('Admin/js/datagrid/datatables/datatables.export.js')); ?>"></script>
<script>
$(document).ready(function () {
    $('#dt-basic-example thead tr').clone(true).appendTo('#dt-basic-example thead');
    $('#dt-basic-example thead tr:eq(1) th').each(function (i) {
        var title = $(this).text();
        $(this).html('<input type="text" class="form-control form-control-sm" placeholder="Search ' + title + '" />');

        $('input', this).on('keyup change', function () {
            if (table.column(i).search() !== this.value) {
                table.column(i).search(this.value).draw();
            }
        });
    });
    
    var table = $('#dt-basic-example').DataTable({
        responsive: true,
        orderCellsTop: true,
        fixedHeader: true,
        lengthChange: true,
        dom: "<'row mb-3'<'col-sm-12 col-md-3 d-flex align-items-center justify-content-start'f><'col-sm-12 col-md-9 d-flex align-items-center justify-content-end'B>>" +
            "<'row'<'col-sm-12'tr>>" +
            "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",
        buttons: [
            { extend: 'pageLength', className: 'btn-outline-default' },
            { extend: 'colvis', text: 'Column Visibility', titleAttr: 'Col visibility', className: 'btn-outline-default' },
            { extend: 'pdfHtml5', text: 'PDF', titleAttr: 'Generate PDF', className: 'btn-outline-danger btn-sm mr-1' },
            { extend: 'excelHtml5', text: 'Excel', titleAttr: 'Generate Excel', className: 'btn-outline-success btn-sm mr-1' },
            { extend: 'csvHtml5', text: 'CSV', titleAttr: 'Generate CSV', className: 'btn-outline-primary btn-sm mr-1' },
            { extend: 'copyHtml5', text: 'Copy', titleAttr: 'Copy to clipboard', className: 'btn-outline-primary btn-sm mr-1' },
            { extend: 'print', text: 'Print', titleAttr: 'Print Table', className: 'btn-outline-primary btn-sm' }
        ],
    });
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.index', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\ost_erp\resources\views/admin/Website/Gallery.blade.php ENDPATH**/ ?>