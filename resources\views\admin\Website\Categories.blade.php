@extends('admin.index')
@section('content')
<title>{{trans('admin.Categories')}}</title>
<main id="js-page-content" role="main" class="page-content">
   <ol class="breadcrumb page-breadcrumb">
      <li class="breadcrumb-item"><a href="javascript:void(0);">{{trans('admin.Home')}}</a></li>
      <li class="breadcrumb-item active">{{trans('admin.Categories')}}</li>
      <li class="position-absolute pos-top pos-right d-none d-sm-block"><span class="js-get-date"></span></li>
   </ol>

   <div class="subheader">
      <h1 class="subheader-title">
         <i class='subheader-icon fal fa-list'></i> {{trans('admin.Categories')}}
      </h1>
   </div>

   <div class="row">
      <div class="col-xl-12">
         <div id="panel-1" class="panel">
            <div class="panel-hdr">
               <h2>{{trans('admin.Categories')}} <span class="fw-300"><i>{{trans('admin.Management')}}</i></span></h2>
               <div class="panel-toolbar">
                  <button class="btn btn-panel" data-action="panel-collapse" data-toggle="tooltip" data-offset="0,10" data-original-title="Collapse"></button>
                  <button class="btn btn-panel" data-action="panel-fullscreen" data-toggle="tooltip" data-offset="0,10" data-original-title="Fullscreen"></button>
               </div>
            </div>
            <div class="panel-container show">
               <div class="panel-content">
                  @include('admin.layouts.messages')
                  
                  <!-- Nav tabs -->
                  <ul class="nav nav-tabs" role="tablist">
                     <li class="nav-item">
                        <a class="nav-link active" data-toggle="tab" href="#tab_borders_icons-7" role="tab">
                           <i class="fal fa-plus mr-1"></i> {{trans('admin.Add')}} {{trans('admin.Category')}}
                        </a>
                     </li>
                     <li class="nav-item">
                        <a class="nav-link" data-toggle="tab" href="#tab_borders_icons-8" role="tab">
                           <i class="fal fa-list mr-1"></i> {{trans('admin.Categories')}} {{trans('admin.List')}}
                        </a>
                     </li>
                  </ul>

                  <!-- Tab panes -->
                  <div class="tab-content">
                     <div class="tab-pane fade show active" id="tab_borders_icons-7" role="tabpanel">
                        <form action="{{url('AddCategory')}}" method="post" class="form-row">
                           {!! csrf_field() !!}
                           <div class="col-md-12">
                              <div class="data-def">
                                 <div class="form-row">
                                    <div class="form-group col-md-6">
                                       <label class="form-label">{{trans('admin.Arabic_Name')}}</label>
                                       <input type="text" name="Arabic_Name" class="form-control" required>
                                    </div>
                                    <div class="form-group col-md-6">
                                       <label class="form-label">{{trans('admin.English_Name')}}</label>
                                       <input type="text" name="English_Name" class="form-control" required>
                                    </div>
                                 </div>
                              </div>
                           </div>
                           <div class="col-md-12">
                              <button type="submit" class="btn btn-primary">{{trans('admin.Save')}}</button>
                           </div>
                        </form>
                     </div>

                     <div class="tab-pane fade" id="tab_borders_icons-8" role="tabpanel">
                        <table id="dt-basic-example" class="table table-bordered table-hover table-striped w-100">
                           <thead class="bg-primary-600">
                              <tr>
                                 <th>{{trans('admin.Arabic_Name')}}</th>
                                 <th>{{trans('admin.English_Name')}}</th>
                                 <th>{{trans('admin.Action')}}</th>
                              </tr>
                           </thead>
                           <tbody>
                              @foreach($Categories as $item)
                              <tr>
                                 <td>{{$item->Arabic_Name}}</td>
                                 <td>{{$item->English_Name}}</td>
                                 <td>
                                    <button type="button" class="btn btn-outline-primary btn-sm" data-toggle="modal" data-target="#editModal{{$item->id}}">
                                       <i class="fal fa-edit"></i>
                                    </button>
                                    <a href="{{url('DeleteCategory/'.$item->id)}}" class="btn btn-outline-danger btn-sm" onclick="return confirm('{{trans('admin.AreYouSure')}}')">
                                       <i class="fal fa-trash"></i>
                                    </a>
                                 </td>
                              </tr>
                              @endforeach
                           </tbody>
                        </table>
                     </div>
                  </div>
               </div>
            </div>
         </div>
      </div>
   </div>

   <!-- Edit Modals -->
   @foreach($Categories as $item)
   <div class="modal fade" id="editModal{{$item->id}}" tabindex="-1" role="dialog">
      <div class="modal-dialog modal-lg" role="document">
         <div class="modal-content">
            <div class="modal-header">
               <h5 class="modal-title">{{trans('admin.Edit')}} {{trans('admin.Category')}}</h5>
               <button type="button" class="close" data-dismiss="modal">
                  <span>&times;</span>
               </button>
            </div>
            <form action="{{url('UpdateCategory/'.$item->id)}}" method="post">
               {!! csrf_field() !!}
               <div class="modal-body">
                  <div class="row">
                     <div class="col-md-6">
                        <div class="form-group">
                           <label>{{trans('admin.Arabic_Name')}}</label>
                           <input type="text" name="Arabic_Name" value="{{$item->Arabic_Name}}" class="form-control" required>
                        </div>
                     </div>
                     <div class="col-md-6">
                        <div class="form-group">
                           <label>{{trans('admin.English_Name')}}</label>
                           <input type="text" name="English_Name" value="{{$item->English_Name}}" class="form-control" required>
                        </div>
                     </div>
                  </div>
               </div>
               <div class="modal-footer">
                  <button type="button" class="btn btn-secondary" data-dismiss="modal">{{trans('admin.Close')}}</button>
                  <button type="submit" class="btn btn-primary">{{trans('admin.Update')}}</button>
               </div>
            </form>
         </div>
      </div>
   </div>
   @endforeach

</main>
@endsection
